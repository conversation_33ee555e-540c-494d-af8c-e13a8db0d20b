-- Migration: Analytics & Reporting System
-- Description: Comprehensive analytics and reporting infrastructure
-- Created: 2024-01-16
-- Integration & Services Agent

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Analytics events table for tracking all user activities
CREATE TABLE analytics_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL, -- 'project_completed', 'payment_received', 'skill_endorsed', etc.
  event_category TEXT NOT NULL, -- 'project', 'financial', 'social', 'platform'
  event_data JSONB NOT NULL DEFAULT '{}'::jsonb,
  value DECIMAL(12,2), -- Monetary value if applicable
  metadata JSONB DEFAULT '{}'::jsonb,
  session_id TEXT,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance metrics table for calculated user performance indicators
CREATE TABLE performance_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  metric_type TEXT NOT NULL, -- 'success_rate', 'avg_rating', 'on_time_delivery', 'response_time'
  metric_value DECIMAL(10,4) NOT NULL,
  metric_unit TEXT, -- 'percentage', 'rating', 'hours', 'count'
  period_type TEXT NOT NULL, -- 'daily', 'weekly', 'monthly', 'quarterly', 'yearly'
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  calculation_method TEXT, -- How the metric was calculated
  sample_size INTEGER, -- Number of data points used
  confidence_score DECIMAL(3,2), -- Confidence in the metric (0.0-1.0)
  calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, metric_type, period_type, period_start, period_end)
);

-- Financial summaries table for aggregated financial data
CREATE TABLE financial_summaries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  period_type TEXT NOT NULL, -- 'daily', 'weekly', 'monthly', 'quarterly', 'yearly'
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  
  -- Revenue breakdown
  total_revenue DECIMAL(12,2) DEFAULT 0,
  project_revenue DECIMAL(12,2) DEFAULT 0,
  commission_revenue DECIMAL(12,2) DEFAULT 0,
  bonus_revenue DECIMAL(12,2) DEFAULT 0,
  other_revenue DECIMAL(12,2) DEFAULT 0,
  
  -- Expense breakdown
  total_expenses DECIMAL(12,2) DEFAULT 0,
  platform_fees DECIMAL(12,2) DEFAULT 0,
  transaction_fees DECIMAL(12,2) DEFAULT 0,
  other_expenses DECIMAL(12,2) DEFAULT 0,
  
  -- Calculated fields
  net_profit DECIMAL(12,2) DEFAULT 0,
  profit_margin DECIMAL(5,2), -- Percentage
  
  -- Transaction counts
  transaction_count INTEGER DEFAULT 0,
  project_count INTEGER DEFAULT 0,
  payment_count INTEGER DEFAULT 0,
  
  -- Growth metrics
  revenue_growth_rate DECIMAL(5,2), -- Percentage compared to previous period
  profit_growth_rate DECIMAL(5,2),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, period_type, period_start, period_end)
);

-- Project analytics table for detailed project performance tracking
CREATE TABLE project_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL, -- References projects table
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Timeline metrics
  planned_start_date DATE,
  actual_start_date DATE,
  planned_end_date DATE,
  actual_end_date DATE,
  planned_duration_days INTEGER,
  actual_duration_days INTEGER,
  timeline_efficiency DECIMAL(5,2), -- Percentage
  
  -- Budget metrics
  planned_budget DECIMAL(12,2),
  actual_cost DECIMAL(12,2),
  budget_variance DECIMAL(12,2),
  budget_efficiency DECIMAL(5,2), -- Percentage
  
  -- Quality metrics
  final_rating DECIMAL(3,2),
  client_satisfaction DECIMAL(3,2),
  deliverable_quality DECIMAL(3,2),
  communication_rating DECIMAL(3,2),
  
  -- Team metrics
  team_size INTEGER,
  team_efficiency DECIMAL(5,2),
  collaboration_score DECIMAL(3,2),
  
  -- Outcome metrics
  project_status TEXT, -- 'completed', 'cancelled', 'on_hold'
  success_indicator BOOLEAN,
  repeat_client BOOLEAN,
  referral_generated BOOLEAN,
  
  -- Additional data
  complexity_score INTEGER, -- 1-10 scale
  technology_stack TEXT[],
  skills_used TEXT[],
  lessons_learned TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Custom reports table for user-defined reports
CREATE TABLE custom_reports (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  report_name TEXT NOT NULL,
  report_description TEXT,
  report_type TEXT NOT NULL, -- 'performance', 'financial', 'project', 'custom'
  
  -- Report configuration
  report_config JSONB NOT NULL DEFAULT '{}'::jsonb,
  visualization_config JSONB DEFAULT '{}'::jsonb,
  filter_config JSONB DEFAULT '{}'::jsonb,
  
  -- Scheduling
  schedule_config JSONB DEFAULT '{}'::jsonb, -- Cron-like scheduling
  is_scheduled BOOLEAN DEFAULT false,
  next_run_at TIMESTAMP WITH TIME ZONE,
  last_generated_at TIMESTAMP WITH TIME ZONE,
  
  -- Sharing and permissions
  is_public BOOLEAN DEFAULT false,
  shared_with UUID[], -- Array of user IDs
  access_level TEXT DEFAULT 'private', -- 'private', 'team', 'public'
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  generation_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Report generations table for tracking report executions
CREATE TABLE report_generations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  report_id UUID NOT NULL REFERENCES custom_reports(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Generation details
  generation_type TEXT NOT NULL, -- 'manual', 'scheduled', 'api'
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  
  -- Data period
  data_start_date DATE,
  data_end_date DATE,
  
  -- Results
  result_data JSONB,
  file_url TEXT, -- URL to generated file (PDF, CSV, etc.)
  file_size BIGINT,
  
  -- Performance
  processing_time_ms INTEGER,
  data_points_processed INTEGER,
  
  -- Error handling
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Analytics dashboards table for custom dashboard configurations
CREATE TABLE analytics_dashboards (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  dashboard_name TEXT NOT NULL,
  dashboard_description TEXT,
  
  -- Layout configuration
  layout_config JSONB NOT NULL DEFAULT '{}'::jsonb,
  widget_config JSONB NOT NULL DEFAULT '{}'::jsonb,
  theme_config JSONB DEFAULT '{}'::jsonb,
  
  -- Dashboard settings
  is_default BOOLEAN DEFAULT false,
  is_public BOOLEAN DEFAULT false,
  refresh_interval INTEGER DEFAULT 300, -- Seconds
  
  -- Sharing
  shared_with UUID[],
  access_level TEXT DEFAULT 'private',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User analytics preferences
CREATE TABLE analytics_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Display preferences
  default_time_period TEXT DEFAULT 'last_30_days',
  preferred_currency TEXT DEFAULT 'USD',
  timezone TEXT DEFAULT 'UTC',
  date_format TEXT DEFAULT 'YYYY-MM-DD',
  
  -- Notification preferences
  email_reports BOOLEAN DEFAULT true,
  push_notifications BOOLEAN DEFAULT true,
  weekly_summary BOOLEAN DEFAULT true,
  monthly_summary BOOLEAN DEFAULT true,
  
  -- Privacy settings
  share_anonymous_data BOOLEAN DEFAULT false,
  include_in_benchmarks BOOLEAN DEFAULT true,
  
  -- Advanced settings
  data_retention_days INTEGER DEFAULT 365,
  export_format TEXT DEFAULT 'csv',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- Create indexes for performance
CREATE INDEX idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX idx_analytics_events_event_category ON analytics_events(event_category);
CREATE INDEX idx_analytics_events_created_at ON analytics_events(created_at);
CREATE INDEX idx_analytics_events_user_type_date ON analytics_events(user_id, event_type, created_at);

CREATE INDEX idx_performance_metrics_user_id ON performance_metrics(user_id);
CREATE INDEX idx_performance_metrics_type ON performance_metrics(metric_type);
CREATE INDEX idx_performance_metrics_period ON performance_metrics(period_type, period_start, period_end);
CREATE INDEX idx_performance_metrics_user_period ON performance_metrics(user_id, period_type, period_start);

CREATE INDEX idx_financial_summaries_user_id ON financial_summaries(user_id);
CREATE INDEX idx_financial_summaries_period ON financial_summaries(period_type, period_start, period_end);
CREATE INDEX idx_financial_summaries_user_period ON financial_summaries(user_id, period_type, period_start);

CREATE INDEX idx_project_analytics_user_id ON project_analytics(user_id);
CREATE INDEX idx_project_analytics_project_id ON project_analytics(project_id);
CREATE INDEX idx_project_analytics_status ON project_analytics(project_status);
CREATE INDEX idx_project_analytics_dates ON project_analytics(actual_start_date, actual_end_date);

CREATE INDEX idx_custom_reports_user_id ON custom_reports(user_id);
CREATE INDEX idx_custom_reports_type ON custom_reports(report_type);
CREATE INDEX idx_custom_reports_active ON custom_reports(is_active);
CREATE INDEX idx_custom_reports_scheduled ON custom_reports(is_scheduled, next_run_at);

CREATE INDEX idx_report_generations_report_id ON report_generations(report_id);
CREATE INDEX idx_report_generations_user_id ON report_generations(user_id);
CREATE INDEX idx_report_generations_status ON report_generations(status);
CREATE INDEX idx_report_generations_created_at ON report_generations(created_at);

CREATE INDEX idx_analytics_dashboards_user_id ON analytics_dashboards(user_id);
CREATE INDEX idx_analytics_dashboards_default ON analytics_dashboards(user_id, is_default);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_financial_summaries_updated_at BEFORE UPDATE ON financial_summaries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_project_analytics_updated_at BEFORE UPDATE ON project_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_custom_reports_updated_at BEFORE UPDATE ON custom_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_analytics_dashboards_updated_at BEFORE UPDATE ON analytics_dashboards FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_analytics_preferences_updated_at BEFORE UPDATE ON analytics_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE report_generations ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_dashboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_preferences ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can view their own analytics events" ON analytics_events FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own analytics events" ON analytics_events FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own performance metrics" ON performance_metrics FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own financial summaries" ON financial_summaries FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own project analytics" ON project_analytics FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own custom reports" ON custom_reports FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view public reports" ON custom_reports FOR SELECT USING (is_public = true);

CREATE POLICY "Users can view their own report generations" ON report_generations FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own dashboards" ON analytics_dashboards FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view public dashboards" ON analytics_dashboards FOR SELECT USING (is_public = true);

CREATE POLICY "Users can manage their own analytics preferences" ON analytics_preferences FOR ALL USING (auth.uid() = user_id);

-- Table comments
COMMENT ON TABLE analytics_events IS 'Tracks all user activities and events for analytics';
COMMENT ON TABLE performance_metrics IS 'Calculated performance indicators for users';
COMMENT ON TABLE financial_summaries IS 'Aggregated financial data by time periods';
COMMENT ON TABLE project_analytics IS 'Detailed project performance tracking';
COMMENT ON TABLE custom_reports IS 'User-defined custom reports and configurations';
COMMENT ON TABLE report_generations IS 'Tracks report generation executions and results';
COMMENT ON TABLE analytics_dashboards IS 'Custom dashboard configurations for users';
COMMENT ON TABLE analytics_preferences IS 'User preferences for analytics and reporting';
