-- Ensure Course Catalog Table Exists
-- Creates the course_catalog table if it doesn't exist for learning paths integration

-- Create course_catalog table if it doesn't exist
CREATE TABLE IF NOT EXISTS course_catalog (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- External reference (e.g., YouTube video ID)
  external_id TEXT NOT NULL,
  provider TEXT NOT NULL DEFAULT 'youtube',
  
  -- Course information
  title TEXT NOT NULL,
  description TEXT,
  duration_minutes INTEGER,
  instructor_name TEXT,
  thumbnail_url TEXT,
  course_url TEXT NOT NULL,
  
  -- Content organization
  skills TEXT[] DEFAULT '{}',
  categories TEXT[] DEFAULT '{}',
  difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')) DEFAULT 'beginner',
  
  -- Status and quality
  is_active BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  rating DECIMAL(3,2) DEFAULT 0,
  
  -- Metadata
  metadata JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique external content per provider
  UNIQUE(external_id, provider)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_course_catalog_provider ON course_catalog(provider);
CREATE INDEX IF NOT EXISTS idx_course_catalog_active ON course_catalog(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_course_catalog_featured ON course_catalog(is_featured) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS idx_course_catalog_difficulty ON course_catalog(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_course_catalog_skills ON course_catalog USING gin(skills);
CREATE INDEX IF NOT EXISTS idx_course_catalog_categories ON course_catalog USING gin(categories);
CREATE INDEX IF NOT EXISTS idx_course_catalog_rating ON course_catalog(rating DESC);

-- Now we can safely add the foreign key constraint to learning_path_videos
DO $$ 
BEGIN
  -- Check if the constraint already exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'learning_path_videos_course_id_fkey'
    AND table_name = 'learning_path_videos'
  ) THEN
    -- Add the foreign key constraint
    ALTER TABLE learning_path_videos 
    ADD CONSTRAINT learning_path_videos_course_id_fkey 
    FOREIGN KEY (course_id) REFERENCES course_catalog(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_course_catalog_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS trigger_course_catalog_updated_at ON course_catalog;
CREATE TRIGGER trigger_course_catalog_updated_at
  BEFORE UPDATE ON course_catalog
  FOR EACH ROW
  EXECUTE FUNCTION update_course_catalog_updated_at();

-- Add RLS policies
ALTER TABLE course_catalog ENABLE ROW LEVEL SECURITY;

-- Anyone can view active courses
CREATE POLICY "Anyone can view active courses" ON course_catalog
  FOR SELECT USING (is_active = true);

-- Admins can manage all courses
CREATE POLICY "Admins can manage courses" ON course_catalog
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND (auth.users.raw_user_meta_data->>'role' = 'admin' OR auth.users.email LIKE '%@royaltea.dev')
    )
  );

-- Grant permissions
GRANT SELECT ON course_catalog TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON course_catalog TO authenticated;

-- Comments
COMMENT ON TABLE course_catalog IS 'Catalog of educational courses and videos from various providers';
COMMENT ON COLUMN course_catalog.external_id IS 'External identifier (e.g., YouTube video ID)';
COMMENT ON COLUMN course_catalog.provider IS 'Content provider (youtube, linkedin, etc.)';
COMMENT ON COLUMN course_catalog.skills IS 'Array of skills taught in this course';
COMMENT ON COLUMN course_catalog.categories IS 'Array of categories this course belongs to';
COMMENT ON COLUMN course_catalog.metadata IS 'Additional metadata including vetting info, analytics, etc.';
