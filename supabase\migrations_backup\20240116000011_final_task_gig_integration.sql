-- Final Task-Gig Integration System (All Issues Fixed)
-- This migration fixes all column name issues and creates a working task-gig integration

-- First, let's check the actual structure of collaboration_requests table
DO $$
DECLARE
    col_exists boolean;
BEGIN
    -- Check if requester_id column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'collaboration_requests' 
        AND column_name = 'requester_id'
    ) INTO col_exists;
    
    -- If requester_id doesn't exist but requested_by does, add an alias or update
    IF NOT col_exists THEN
        -- Check if requested_by exists instead
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'collaboration_requests' 
            AND column_name = 'requested_by'
        ) INTO col_exists;
        
        IF col_exists THEN
            -- Add requester_id as an alias to requested_by
            ALTER TABLE collaboration_requests ADD COLUMN IF NOT EXISTS requester_id UUID;
            UPDATE collaboration_requests SET requester_id = requested_by WHERE requester_id IS NULL;
        END IF;
    END IF;
END
$$;

-- Create collaboration_request_applications table if it doesn't exist
CREATE TABLE IF NOT EXISTS collaboration_request_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    request_id UUID NOT NULL REFERENCES collaboration_requests(id) ON DELETE CASCADE,
    applicant_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'withdrawn')),
    application_message TEXT,
    proposed_rate DECIMAL(10,2),
    proposed_timeline_days INTEGER,
    portfolio_links TEXT[],
    relevant_experience TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES auth.users(id),
    review_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(request_id, applicant_id)
);

-- Task-Gig relationship tracking
CREATE TABLE IF NOT EXISTS task_gig_relationships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Relationship details
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
  gig_id UUID REFERENCES collaboration_requests(id) ON DELETE CASCADE,
  relationship_type TEXT NOT NULL CHECK (relationship_type IN ('task_to_gig', 'gig_to_task', 'bidirectional')),
  
  -- Conversion metadata
  converted_by UUID NOT NULL REFERENCES auth.users(id),
  conversion_reason TEXT,
  original_type TEXT NOT NULL CHECK (original_type IN ('task', 'gig')),
  
  -- Sync settings
  auto_sync_status BOOLEAN DEFAULT true,
  auto_sync_assignee BOOLEAN DEFAULT true,
  auto_sync_deadline BOOLEAN DEFAULT true,
  
  -- Status tracking
  is_active BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique relationships
  UNIQUE(task_id, gig_id)
);

-- Gig application to task assignment tracking
CREATE TABLE IF NOT EXISTS gig_task_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Assignment details
  gig_id UUID NOT NULL REFERENCES collaboration_requests(id) ON DELETE CASCADE,
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  application_id UUID REFERENCES collaboration_request_applications(id) ON DELETE SET NULL,
  
  -- Assignment metadata
  assigned_user_id UUID NOT NULL REFERENCES auth.users(id),
  assigned_by UUID NOT NULL REFERENCES auth.users(id),
  assignment_type TEXT DEFAULT 'gig_application' CHECK (assignment_type IN ('gig_application', 'direct_assignment', 'auto_assignment')),
  
  -- Terms and conditions
  agreed_rate DECIMAL(10,2),
  agreed_deadline TIMESTAMP WITH TIME ZONE,
  agreed_deliverables TEXT[],
  
  -- Status tracking
  status TEXT DEFAULT 'assigned' CHECK (status IN ('assigned', 'in_progress', 'completed', 'cancelled')),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add columns to existing tables if they don't exist
DO $$
BEGIN
    -- Add columns to collaboration_requests
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'collaboration_requests' AND column_name = 'source_task_id') THEN
        ALTER TABLE collaboration_requests ADD COLUMN source_task_id UUID REFERENCES tasks(id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'collaboration_requests' AND column_name = 'auto_assign_on_acceptance') THEN
        ALTER TABLE collaboration_requests ADD COLUMN auto_assign_on_acceptance BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'collaboration_requests' AND column_name = 'task_integration_settings') THEN
        ALTER TABLE collaboration_requests ADD COLUMN task_integration_settings JSONB DEFAULT '{}'::jsonb;
    END IF;
    
    -- Add columns to tasks
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'source_gig_id') THEN
        ALTER TABLE tasks ADD COLUMN source_gig_id UUID REFERENCES collaboration_requests(id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'is_gig_derived') THEN
        ALTER TABLE tasks ADD COLUMN is_gig_derived BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'gig_integration_settings') THEN
        ALTER TABLE tasks ADD COLUMN gig_integration_settings JSONB DEFAULT '{}'::jsonb;
    END IF;
END
$$;

-- Function to convert task to gig (handles both requester_id and requested_by)
CREATE OR REPLACE FUNCTION convert_task_to_gig(
  p_task_id UUID,
  p_user_id UUID,
  p_budget_min DECIMAL DEFAULT NULL,
  p_budget_max DECIMAL DEFAULT NULL,
  p_timeline_days INTEGER DEFAULT NULL,
  p_skill_requirements TEXT[] DEFAULT NULL,
  p_conversion_reason TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_task_record RECORD;
  v_gig_id UUID;
  v_relationship_id UUID;
  v_requester_column TEXT;
BEGIN
  -- Get task details
  SELECT * INTO v_task_record
  FROM tasks
  WHERE id = p_task_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Task not found: %', p_task_id;
  END IF;
  
  -- Determine which column to use for requester
  SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'collaboration_requests' AND column_name = 'requester_id') 
    THEN 'requester_id'
    ELSE 'requested_by'
  END INTO v_requester_column;
  
  -- Create collaboration request (gig) using dynamic SQL
  EXECUTE format('
    INSERT INTO collaboration_requests (
      title,
      description,
      project_id,
      %I,
      request_type,
      budget_min,
      budget_max,
      timeline_days,
      skill_requirements,
      status,
      source_task_id,
      auto_assign_on_acceptance,
      task_integration_settings
    ) VALUES (
      $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
    ) RETURNING id',
    v_requester_column
  ) 
  USING 
    v_task_record.title,
    COALESCE(v_task_record.description, 'Converted from task'),
    v_task_record.project_id,
    p_user_id,
    'task_assistance',
    p_budget_min,
    p_budget_max,
    p_timeline_days,
    p_skill_requirements,
    'open',
    p_task_id,
    true,
    jsonb_build_object(
      'original_task_id', p_task_id,
      'auto_sync_enabled', true,
      'conversion_date', NOW()
    )
  INTO v_gig_id;
  
  -- Create relationship record
  INSERT INTO task_gig_relationships (
    task_id,
    gig_id,
    relationship_type,
    converted_by,
    conversion_reason,
    original_type
  ) VALUES (
    p_task_id,
    v_gig_id,
    'task_to_gig',
    p_user_id,
    p_conversion_reason,
    'task'
  ) RETURNING id INTO v_relationship_id;
  
  -- Update task to reference gig
  UPDATE tasks
  SET 
    source_gig_id = v_gig_id,
    is_gig_derived = false, -- This is the original task
    gig_integration_settings = jsonb_build_object(
      'converted_to_gig', true,
      'gig_id', v_gig_id,
      'relationship_id', v_relationship_id
    )
  WHERE id = p_task_id;
  
  RETURN v_gig_id;
END;
$$ LANGUAGE plpgsql;

-- Function to convert gig to task
CREATE OR REPLACE FUNCTION convert_gig_to_task(
  p_gig_id UUID,
  p_user_id UUID,
  p_project_id UUID,
  p_assignee_id UUID DEFAULT NULL,
  p_conversion_reason TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_gig_record RECORD;
  v_task_id UUID;
  v_relationship_id UUID;
BEGIN
  -- Get gig details
  SELECT * INTO v_gig_record
  FROM collaboration_requests
  WHERE id = p_gig_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Gig not found: %', p_gig_id;
  END IF;
  
  -- Create task
  INSERT INTO tasks (
    title,
    description,
    project_id,
    assignee_id,
    created_by,
    status,
    task_type,
    difficulty_level,
    estimated_hours,
    source_gig_id,
    is_gig_derived,
    gig_integration_settings
  ) VALUES (
    v_gig_record.title,
    COALESCE(v_gig_record.description, 'Converted from gig'),
    p_project_id,
    p_assignee_id,
    p_user_id,
    'todo',
    'development',
    'medium',
    COALESCE(v_gig_record.timeline_days * 8, 8), -- Estimate 8 hours per day
    p_gig_id,
    true,
    jsonb_build_object(
      'original_gig_id', p_gig_id,
      'auto_sync_enabled', true,
      'conversion_date', NOW()
    )
  ) RETURNING id INTO v_task_id;
  
  -- Create relationship record
  INSERT INTO task_gig_relationships (
    task_id,
    gig_id,
    relationship_type,
    converted_by,
    conversion_reason,
    original_type
  ) VALUES (
    v_task_id,
    p_gig_id,
    'gig_to_task',
    p_user_id,
    p_conversion_reason,
    'gig'
  ) RETURNING id INTO v_relationship_id;
  
  -- Update gig to reference task
  UPDATE collaboration_requests
  SET 
    source_task_id = v_task_id,
    task_integration_settings = jsonb_build_object(
      'converted_to_task', true,
      'task_id', v_task_id,
      'relationship_id', v_relationship_id
    )
  WHERE id = p_gig_id;
  
  RETURN v_task_id;
END;
$$ LANGUAGE plpgsql;

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_task_gig_relationships_task_id ON task_gig_relationships(task_id);
CREATE INDEX IF NOT EXISTS idx_task_gig_relationships_gig_id ON task_gig_relationships(gig_id);
CREATE INDEX IF NOT EXISTS idx_gig_task_assignments_gig_id ON gig_task_assignments(gig_id);
CREATE INDEX IF NOT EXISTS idx_gig_task_assignments_task_id ON gig_task_assignments(task_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_request_applications_request ON collaboration_request_applications(request_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_request_applications_applicant ON collaboration_request_applications(applicant_id);

-- Enable RLS
ALTER TABLE task_gig_relationships ENABLE ROW LEVEL SECURITY;
ALTER TABLE gig_task_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE collaboration_request_applications ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view task-gig relationships for their content" ON task_gig_relationships
  FOR SELECT USING (
    task_id IN (SELECT id FROM tasks WHERE assignee_id = auth.uid() OR created_by = auth.uid()) OR
    gig_id IN (SELECT id FROM collaboration_requests WHERE 
      COALESCE(requester_id, requested_by) = auth.uid())
  );

CREATE POLICY "Users can create task-gig relationships for their content" ON task_gig_relationships
  FOR INSERT WITH CHECK (
    converted_by = auth.uid() AND (
      task_id IN (SELECT id FROM tasks WHERE assignee_id = auth.uid() OR created_by = auth.uid()) OR
      gig_id IN (SELECT id FROM collaboration_requests WHERE 
        COALESCE(requester_id, requested_by) = auth.uid())
    )
  );

CREATE POLICY "Users can view relevant gig-task assignments" ON gig_task_assignments
  FOR SELECT USING (
    assigned_user_id = auth.uid() OR
    assigned_by = auth.uid() OR
    gig_id IN (SELECT id FROM collaboration_requests WHERE 
      COALESCE(requester_id, requested_by) = auth.uid()) OR
    task_id IN (SELECT id FROM tasks WHERE assignee_id = auth.uid() OR created_by = auth.uid())
  );

CREATE POLICY "Users can view applications for their requests" ON collaboration_request_applications
  FOR SELECT USING (
    request_id IN (SELECT id FROM collaboration_requests WHERE 
      COALESCE(requester_id, requested_by) = auth.uid())
  );

CREATE POLICY "Users can view their own applications" ON collaboration_request_applications
  FOR SELECT USING (applicant_id = auth.uid());

CREATE POLICY "Users can create applications" ON collaboration_request_applications
  FOR INSERT WITH CHECK (applicant_id = auth.uid());

-- Comments
COMMENT ON TABLE task_gig_relationships IS 'Tracks relationships between tasks and gigs for bidirectional integration';
COMMENT ON TABLE gig_task_assignments IS 'Manages assignments when gig applications are accepted';
COMMENT ON TABLE collaboration_request_applications IS 'Applications for collaboration requests (gigs)';
COMMENT ON FUNCTION convert_task_to_gig IS 'Converts a task into a gig with proper relationship tracking (handles both requester_id and requested_by columns)';
COMMENT ON FUNCTION convert_gig_to_task IS 'Converts a gig into a task with proper relationship tracking';
