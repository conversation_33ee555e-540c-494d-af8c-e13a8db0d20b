import React, { useState, useEffect, useContext } from 'react';
import {
  Card, CardBody, CardHeader, Button, Progress, Chip, Badge,
  Modal, ModalContent, ModalHeader, ModalBody, ModalFooter,
  Input, Textarea, Select, SelectItem, Checkbox, Divider,
  Dropdown, DropdownTrigger, DropdownMenu, DropdownItem,
  useDisclosure, Spinner, Tabs, Tab, Avatar
} from '@heroui/react';
import { motion, Reorder } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import {
  Play, Plus, Edit, Trash2, Share2, Clock, BookOpen,
  CheckCircle, Circle, MoreVertical, DragHandleDots2,
  Target, TrendingUp, Users, Star, Filter, Search,
  Calendar, BarChart3, Award, Zap
} from 'lucide-react';
import ShareModal from './ShareModal';

/**
 * Enhanced Learning Queue Component
 *
 * Comprehensive personal learning queue system with better UI, progress tracking,
 * queue management, and integration with shared content and smart recommendations.
 */
const LearningQueue = ({ isOpen, onClose, currentUser: propCurrentUser }) => {
  const { currentUser: contextUser } = useContext(UserContext);
  const currentUser = propCurrentUser || contextUser;

  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('my-queues');
  const [queues, setQueues] = useState([]);
  const [selectedQueue, setSelectedQueue] = useState(null);
  const [queueItems, setQueueItems] = useState([]);
  const [sharedQueues, setSharedQueues] = useState([]);
  const [queueStats, setQueueStats] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  const { isOpen: isCreateOpen, onOpen: onCreateOpen, onClose: onCreateClose } = useDisclosure();
  const { isOpen: isAddItemOpen, onOpen: onAddItemOpen, onClose: onAddItemClose } = useDisclosure();
  const { isOpen: isShareOpen, onOpen: onShareOpen, onClose: onShareClose } = useDisclosure();
  
  const [createForm, setCreateForm] = useState({
    name: '',
    description: '',
    color: '#3b82f6',
    icon: 'BookOpen',
    isPublic: false,
    autoAdvance: true,
    difficulty_level: 'mixed',
    estimated_duration_hours: '',
    tags: []
  });

  const [addItemForm, setAddItemForm] = useState({
    itemType: 'video',
    title: '',
    description: '',
    url: '',
    provider: 'youtube',
    durationMinutes: '',
    difficultyLevel: 'beginner',
    skills: [],
    notes: ''
  });

  // Load user's learning queues and related data
  useEffect(() => {
    if (isOpen && currentUser) {
      loadAllData();
    }
  }, [isOpen, currentUser, activeTab]);

  // Load queue items when queue is selected
  useEffect(() => {
    if (selectedQueue) {
      loadQueueItems(selectedQueue.id);
    }
  }, [selectedQueue]);

  const loadAllData = async () => {
    try {
      setLoading(true);

      // Load user's own queues with enhanced data
      const { data: userQueues, error: queuesError } = await supabase
        .from('learning_queues')
        .select(`
          *,
          learning_queue_items(
            id,
            order_index,
            status,
            completed_at,
            notes,
            course_catalog(*),
            learning_progress(completion_percentage, time_spent_minutes)
          )
        `)
        .eq('user_id', currentUser.id)
        .order('updated_at', { ascending: false });

      if (queuesError) throw queuesError;

      // Load shared queues if on shared tab
      let publicQueues = [];
      if (activeTab === 'shared') {
        const { data: sharedData, error: sharedError } = await supabase
          .from('learning_queues')
          .select(`
            *,
            profiles!learning_queues_user_id_fkey(full_name, avatar_url),
            learning_queue_items(count)
          `)
          .eq('is_public', true)
          .neq('user_id', currentUser.id)
          .order('created_at', { ascending: false })
          .limit(20);

        if (sharedError) throw sharedError;
        publicQueues = sharedData || [];
      }

      // Calculate queue statistics
      const stats = calculateQueueStats(userQueues || []);

      setQueues(userQueues || []);
      setSharedQueues(publicQueues);
      setQueueStats(stats);

      // Select first queue if none selected
      if (userQueues && userQueues.length > 0 && !selectedQueue) {
        setSelectedQueue(userQueues[0]);
      }

    } catch (error) {
      console.error('Error loading queue data:', error);
      toast.error('Failed to load learning queues');
    } finally {
      setLoading(false);
    }
  };

  const calculateQueueStats = (queues) => {
    const stats = {
      totalQueues: queues.length,
      totalItems: 0,
      completedItems: 0,
      inProgressItems: 0,
      totalTimeSpent: 0,
      averageProgress: 0
    };

    queues.forEach(queue => {
      const items = queue.learning_queue_items || [];
      stats.totalItems += items.length;

      items.forEach(item => {
        if (item.status === 'completed') {
          stats.completedItems++;
        } else if (item.status === 'in_progress') {
          stats.inProgressItems++;
        }

        if (item.learning_progress?.[0]) {
          stats.totalTimeSpent += item.learning_progress[0].time_spent_minutes || 0;
        }
      });
    });

    if (stats.totalItems > 0) {
      stats.averageProgress = (stats.completedItems / stats.totalItems) * 100;
    }

    return stats;
  };

  const calculateQueueProgress = (items) => {
    if (!items || items.length === 0) return 0;
    const completedItems = items.filter(item => item.status === 'completed').length;
    return (completedItems / items.length) * 100;
  };

  // Duplicate a shared queue to user's own queues
  const duplicateQueue = async (sharedQueue) => {
    try {
      setLoading(true);

      // Create new queue based on shared queue
      const newQueueData = {
        user_id: currentUser.id,
        name: `${sharedQueue.name} (Copy)`,
        description: sharedQueue.description,
        color: sharedQueue.color || '#3b82f6',
        icon: sharedQueue.icon || 'BookOpen',
        difficulty_level: sharedQueue.difficulty_level,
        estimated_duration_hours: sharedQueue.estimated_duration_hours,
        tags: sharedQueue.tags || [],
        is_public: false,
        auto_advance: true
      };

      const { data: newQueue, error: queueError } = await supabase
        .from('learning_queues')
        .insert([newQueueData])
        .select()
        .single();

      if (queueError) throw queueError;

      // Copy queue items if any
      if (sharedQueue.learning_queue_items?.length > 0) {
        const itemsData = sharedQueue.learning_queue_items.map((item, index) => ({
          queue_id: newQueue.id,
          course_id: item.course_id,
          order_index: index + 1,
          status: 'not_started',
          notes: ''
        }));

        const { error: itemsError } = await supabase
          .from('learning_queue_items')
          .insert(itemsData);

        if (itemsError) throw itemsError;
      }

      toast.success('Queue added to your collection!');
      loadAllData(); // Refresh data
      setActiveTab('my-queues'); // Switch to my queues tab

    } catch (error) {
      console.error('Error duplicating queue:', error);
      toast.error('Failed to add queue to your collection');
    } finally {
      setLoading(false);
    }
  };

  // View shared queue details
  const viewSharedQueue = (queue) => {
    setSelectedQueue(queue);
    // Could open a preview modal or navigate to a detailed view
    toast.info('Queue preview feature coming soon!');
  };

  const loadQueueItems = async (queueId) => {
    try {
      const { data, error } = await supabase
        .from('learning_queue_items')
        .select('*')
        .eq('queue_id', queueId)
        .order('sequence_order', { ascending: true });

      if (error) throw error;
      
      setQueueItems(data || []);
      
    } catch (error) {
      console.error('Error loading queue items:', error);
      toast.error('Failed to load queue items');
    }
  };

  // Create new learning queue
  const handleCreateQueue = async () => {
    if (!createForm.name.trim()) {
      toast.error('Queue name is required');
      return;
    }

    try {
      setLoading(true);

      const queueData = {
        user_id: currentUser.id,
        name: createForm.name.trim(),
        description: createForm.description.trim(),
        color: createForm.color,
        icon: createForm.icon,
        is_public: createForm.isPublic,
        auto_advance: createForm.autoAdvance
      };

      const { data, error } = await supabase
        .from('learning_queues')
        .insert([queueData])
        .select()
        .single();

      if (error) throw error;

      toast.success('Learning queue created successfully!');
      
      // Reset form and close modal
      setCreateForm({
        name: '',
        description: '',
        color: '#3b82f6',
        icon: 'bi-collection-play',
        isPublic: false,
        autoAdvance: true
      });
      
      onCreateClose();
      loadQueues();
      
    } catch (error) {
      console.error('Error creating queue:', error);
      toast.error('Failed to create learning queue');
    } finally {
      setLoading(false);
    }
  };

  // Add item to queue
  const handleAddItem = async () => {
    if (!addItemForm.title.trim() || !selectedQueue) {
      toast.error('Item title is required');
      return;
    }

    try {
      setLoading(true);

      // Get next sequence order
      const nextOrder = queueItems.length > 0 
        ? Math.max(...queueItems.map(item => item.sequence_order)) + 1 
        : 1;

      // Extract video ID if it's a YouTube URL
      let externalId = addItemForm.url;
      if (addItemForm.itemType === 'video' && addItemForm.provider === 'youtube') {
        const videoIdMatch = addItemForm.url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/);
        externalId = videoIdMatch ? videoIdMatch[1] : addItemForm.url;
      }

      const itemData = {
        queue_id: selectedQueue.id,
        item_type: addItemForm.itemType,
        external_id: externalId,
        title: addItemForm.title.trim(),
        description: addItemForm.description.trim(),
        url: addItemForm.url.trim(),
        provider: addItemForm.provider,
        duration_minutes: addItemForm.durationMinutes ? parseInt(addItemForm.durationMinutes) : null,
        difficulty_level: addItemForm.difficultyLevel,
        skills: addItemForm.skills,
        sequence_order: nextOrder,
        thumbnail_url: addItemForm.itemType === 'video' && addItemForm.provider === 'youtube' 
          ? `https://img.youtube.com/vi/${externalId}/mqdefault.jpg` 
          : null
      };

      const { error } = await supabase
        .from('learning_queue_items')
        .insert([itemData]);

      if (error) throw error;

      toast.success('Item added to queue!');
      
      // Reset form and close modal
      setAddItemForm({
        itemType: 'video',
        title: '',
        description: '',
        url: '',
        provider: 'youtube',
        durationMinutes: '',
        difficultyLevel: 'beginner',
        skills: []
      });
      
      onAddItemClose();
      loadQueueItems(selectedQueue.id);
      
    } catch (error) {
      console.error('Error adding item:', error);
      toast.error('Failed to add item to queue');
    } finally {
      setLoading(false);
    }
  };

  // Update item status
  const updateItemStatus = async (itemId, status) => {
    try {
      const updateData = { status };
      
      if (status === 'completed') {
        updateData.completed_at = new Date().toISOString();
        updateData.progress_percentage = 100;
      } else if (status === 'in_progress') {
        updateData.started_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from('learning_queue_items')
        .update(updateData)
        .eq('id', itemId);

      if (error) throw error;

      loadQueueItems(selectedQueue.id);
      
    } catch (error) {
      console.error('Error updating item status:', error);
      toast.error('Failed to update item status');
    }
  };

  // Reorder queue items
  const handleReorder = async (newOrder) => {
    setQueueItems(newOrder);
    
    try {
      // Update sequence orders in database
      const updates = newOrder.map((item, index) => ({
        id: item.id,
        sequence_order: index + 1
      }));

      for (const update of updates) {
        await supabase
          .from('learning_queue_items')
          .update({ sequence_order: update.sequence_order })
          .eq('id', update.id);
      }
      
    } catch (error) {
      console.error('Error reordering items:', error);
      toast.error('Failed to reorder items');
      // Reload to restore original order
      loadQueueItems(selectedQueue.id);
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'warning';
      case 'skipped': return 'default';
      default: return 'primary';
    }
  };

  // Calculate queue progress
  const getQueueProgress = (queue) => {
    if (queue.total_items === 0) return 0;
    return Math.round((queue.completed_items / queue.total_items) * 100);
  };

  if (loading && queues.length === 0) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="5xl">
        <ModalContent>
          <ModalBody className="py-8">
            <div className="text-center">
              <Spinner size="lg" />
              <p className="mt-2">Loading learning queues...</p>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    );
  }

  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose} size="6xl" scrollBehavior="inside">
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-2xl font-bold">Learning Queues</h2>
            <p className="text-default-600 font-normal">
              Organize your learning journey with custom queues and discover shared content
            </p>
          </ModalHeader>

          <ModalBody>
            {/* Enhanced Header with Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardBody className="text-center p-4">
                  <BookOpen className="w-8 h-8 mx-auto mb-2 text-primary" />
                  <div className="text-2xl font-bold text-primary">{queueStats.totalQueues}</div>
                  <div className="text-sm text-default-600">My Queues</div>
                </CardBody>
              </Card>
              <Card>
                <CardBody className="text-center p-4">
                  <Target className="w-8 h-8 mx-auto mb-2 text-warning" />
                  <div className="text-2xl font-bold text-warning">{queueStats.totalItems}</div>
                  <div className="text-sm text-default-600">Total Items</div>
                </CardBody>
              </Card>
              <Card>
                <CardBody className="text-center p-4">
                  <CheckCircle className="w-8 h-8 mx-auto mb-2 text-success" />
                  <div className="text-2xl font-bold text-success">{queueStats.completedItems}</div>
                  <div className="text-sm text-default-600">Completed</div>
                </CardBody>
              </Card>
              <Card>
                <CardBody className="text-center p-4">
                  <BarChart3 className="w-8 h-8 mx-auto mb-2 text-secondary" />
                  <div className="text-2xl font-bold text-secondary">{Math.round(queueStats.averageProgress)}%</div>
                  <div className="text-sm text-default-600">Avg Progress</div>
                </CardBody>
              </Card>
            </div>

            {/* Main Content with Tabs */}
            <Tabs selectedKey={activeTab} onSelectionChange={setActiveTab} className="w-full">
              <Tab key="my-queues" title={
                <div className="flex items-center gap-2">
                  <BookOpen className="w-4 h-4" />
                  <span>My Queues</span>
                </div>
              }>
                <div className="flex gap-6 h-[500px]">
                  {/* Queue Sidebar */}
                  <div className="w-80 flex-shrink-0 space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold">My Queues ({queues.length})</h3>
                      <Button
                        size="sm"
                        color="primary"
                        onPress={onCreateOpen}
                        startContent={<Plus className="w-4 h-4" />}
                      >
                        New Queue
                      </Button>
                    </div>

                    <div className="space-y-2 max-h-[450px] overflow-y-auto">
                      {queues.map((queue) => (
                        <Card
                          key={queue.id}
                          className={`cursor-pointer transition-all ${
                            selectedQueue?.id === queue.id
                              ? 'ring-2 ring-primary bg-primary/10'
                              : 'hover:shadow-md'
                          }`}
                          onPress={() => setSelectedQueue(queue)}
                        >
                          <CardBody className="p-3">
                            <div className="flex items-center gap-3">
                              <div
                                className="w-3 h-3 rounded-full flex-shrink-0"
                                style={{ backgroundColor: queue.color }}
                              />
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium truncate">{queue.name}</h4>
                                <div className="flex items-center gap-2 text-xs text-default-500">
                                  <span>{queue.learning_queue_items?.length || 0} items</span>
                                  {queue.is_public && (
                                    <Chip size="sm" variant="flat" color="secondary">Public</Chip>
                                  )}
                                </div>
                                {queue.learning_queue_items?.length > 0 && (
                                  <Progress
                                    value={calculateQueueProgress(queue.learning_queue_items)}
                                    size="sm"
                                    color="primary"
                                    className="mt-2"
                                  />
                                )}
                              </div>
                            </div>
                          </CardBody>
                        </Card>
                      ))}

                      {queues.length === 0 && (
                        <div className="text-center py-8 text-default-600">
                          <BookOpen className="w-12 h-12 mx-auto mb-3 text-default-400" />
                          <p className="mb-3">No queues yet</p>
                          <Button
                            size="sm"
                            color="primary"
                            onPress={onCreateOpen}
                            startContent={<Plus className="w-4 h-4" />}
                          >
                            Create your first queue
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
            <div className="flex gap-6 h-[600px]">
              {/* Queue Sidebar */}
              <div className="w-80 flex-shrink-0 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">My Queues</h3>
                  <Button
                    size="sm"
                    color="primary"
                    onClick={onCreateOpen}
                  >
                    <i className="bi bi-plus mr-1"></i>
                    New Queue
                  </Button>
                </div>

                <div className="space-y-2 max-h-[500px] overflow-y-auto">
                  {queues.map((queue) => (
                    <Card
                      key={queue.id}
                      className={`cursor-pointer transition-all ${
                        selectedQueue?.id === queue.id 
                          ? 'ring-2 ring-primary bg-primary/10' 
                          : 'hover:shadow-md'
                      }`}
                      onClick={() => setSelectedQueue(queue)}
                    >
                      <CardBody className="p-3">
                        <div className="flex items-center gap-3">
                          <div 
                            className="w-3 h-3 rounded-full flex-shrink-0"
                            style={{ backgroundColor: queue.color }}
                          />
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium truncate">{queue.name}</h4>
                            <div className="flex items-center gap-2 text-xs text-default-500">
                              <span>{queue.total_items} items</span>
                              <span>•</span>
                              <span>{getQueueProgress(queue)}% complete</span>
                            </div>
                          </div>
                          <Chip
                            size="sm"
                            color={queue.status === 'completed' ? 'success' : 'primary'}
                            variant="flat"
                          >
                            {queue.status}
                          </Chip>
                        </div>
                        
                        {queue.total_items > 0 && (
                          <Progress
                            value={getQueueProgress(queue)}
                            color="primary"
                            size="sm"
                            className="mt-2"
                          />
                        )}
                      </CardBody>
                    </Card>
                  ))}

                  {queues.length === 0 && (
                    <div className="text-center py-8 text-default-600">
                      <i className="bi bi-collection text-4xl mb-2 block"></i>
                      <p className="mb-2">No learning queues yet</p>
                      <Button
                        size="sm"
                        color="primary"
                        variant="flat"
                        onClick={onCreateOpen}
                      >
                        Create your first queue
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Queue Content */}
              <div className="flex-1 min-w-0">
                {selectedQueue ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-xl font-semibold">{selectedQueue.name}</h3>
                        {selectedQueue.description && (
                          <p className="text-default-600">{selectedQueue.description}</p>
                        )}
                      </div>
                      <Button
                        size="sm"
                        color="primary"
                        variant="flat"
                        onClick={onAddItemOpen}
                      >
                        <i className="bi bi-plus mr-1"></i>
                        Add Item
                      </Button>
                    </div>

                    {/* Queue Items */}
                    <div className="space-y-2 max-h-[500px] overflow-y-auto">
                      {queueItems.length > 0 ? (
                        <Reorder.Group
                          axis="y"
                          values={queueItems}
                          onReorder={handleReorder}
                          className="space-y-2"
                        >
                          {queueItems.map((item, index) => (
                            <Reorder.Item
                              key={item.id}
                              value={item}
                              className="cursor-move"
                            >
                              <Card className="hover:shadow-md transition-shadow">
                                <CardBody className="p-3">
                                  <div className="flex items-center gap-3">
                                    <div className="flex items-center gap-2">
                                      <span className="text-sm text-default-500 w-6">
                                        {index + 1}
                                      </span>
                                      <i className="bi bi-grip-vertical text-default-400"></i>
                                    </div>

                                    {item.thumbnail_url && (
                                      <img
                                        src={item.thumbnail_url}
                                        alt={item.title}
                                        className="w-16 h-10 object-cover rounded flex-shrink-0"
                                      />
                                    )}

                                    <div className="flex-1 min-w-0">
                                      <h4 className="font-medium truncate">{item.title}</h4>
                                      <div className="flex items-center gap-2 text-xs text-default-500">
                                        <Chip size="sm" variant="bordered">
                                          {item.item_type}
                                        </Chip>
                                        {item.duration_minutes && (
                                          <span>{item.duration_minutes}m</span>
                                        )}
                                        <span>{item.difficulty_level}</span>
                                      </div>
                                    </div>

                                    <div className="flex items-center gap-2">
                                      <Chip
                                        size="sm"
                                        color={getStatusColor(item.status)}
                                        variant="flat"
                                      >
                                        {item.status.replace('_', ' ')}
                                      </Chip>

                                      <Dropdown>
                                        <DropdownTrigger>
                                          <Button
                                            size="sm"
                                            variant="flat"
                                            isIconOnly
                                          >
                                            <i className="bi bi-three-dots"></i>
                                          </Button>
                                        </DropdownTrigger>
                                        <DropdownMenu>
                                          {item.url && (
                                            <DropdownItem
                                              key="open"
                                              startContent={<i className="bi bi-box-arrow-up-right"></i>}
                                              onPress={() => window.open(item.url, '_blank')}
                                            >
                                              Open
                                            </DropdownItem>
                                          )}
                                          {item.status !== 'in_progress' && (
                                            <DropdownItem
                                              key="start"
                                              startContent={<i className="bi bi-play"></i>}
                                              onPress={() => updateItemStatus(item.id, 'in_progress')}
                                            >
                                              Start
                                            </DropdownItem>
                                          )}
                                          {item.status !== 'completed' && (
                                            <DropdownItem
                                              key="complete"
                                              startContent={<i className="bi bi-check"></i>}
                                              onPress={() => updateItemStatus(item.id, 'completed')}
                                            >
                                              Mark Complete
                                            </DropdownItem>
                                          )}
                                          <DropdownItem
                                            key="skip"
                                            startContent={<i className="bi bi-skip-forward"></i>}
                                            onPress={() => updateItemStatus(item.id, 'skipped')}
                                          >
                                            Skip
                                          </DropdownItem>
                                        </DropdownMenu>
                                      </Dropdown>
                                    </div>
                                  </div>

                                  {item.progress_percentage > 0 && item.progress_percentage < 100 && (
                                    <Progress
                                      value={item.progress_percentage}
                                      color="warning"
                                      size="sm"
                                      className="mt-2"
                                    />
                                  )}
                                </CardBody>
                              </Card>
                            </Reorder.Item>
                          ))}
                        </Reorder.Group>
                      ) : (
                        <div className="text-center py-8 text-default-600">
                          <i className="bi bi-collection-play text-4xl mb-2 block"></i>
                          <p className="mb-2">No items in this queue yet</p>
                          <Button
                            size="sm"
                            color="primary"
                            variant="flat"
                            onClick={onAddItemOpen}
                          >
                            Add your first item
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-16 text-default-600">
                    <i className="bi bi-arrow-left text-4xl mb-2 block"></i>
                    <p>Select a queue to view its contents</p>
                  </div>
                )}
              </div>
            </div>
          </div>
          </Tab>

          <Tab key="shared" title={
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span>Shared Queues</span>
            </div>
          }>
            <div className="space-y-4">
              {/* Search and Filter */}
              <div className="flex gap-4">
                <Input
                  placeholder="Search shared queues..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  startContent={<Search className="w-4 h-4 text-default-400" />}
                  className="flex-1"
                />
                <Select
                  placeholder="Filter by status"
                  selectedKeys={filterStatus !== 'all' ? [filterStatus] : []}
                  onSelectionChange={(keys) => setFilterStatus(Array.from(keys)[0] || 'all')}
                  className="w-48"
                >
                  <SelectItem key="all">All Queues</SelectItem>
                  <SelectItem key="popular">Popular</SelectItem>
                  <SelectItem key="recent">Recently Added</SelectItem>
                </Select>
              </div>

              {/* Shared Queues Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[400px] overflow-y-auto">
                {sharedQueues
                  .filter(queue =>
                    !searchTerm ||
                    queue.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    queue.description?.toLowerCase().includes(searchTerm.toLowerCase())
                  )
                  .map((queue) => (
                    <Card key={queue.id} className="hover:shadow-lg transition-shadow">
                      <CardBody className="p-4">
                        <div className="flex items-start gap-3 mb-3">
                          <Avatar
                            size="sm"
                            src={queue.profiles?.avatar_url}
                            name={queue.profiles?.full_name || 'Unknown'}
                          />
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold line-clamp-1">{queue.name}</h4>
                            <p className="text-sm text-default-600">
                              by {queue.profiles?.full_name || 'Unknown User'}
                            </p>
                          </div>
                          <Button
                            size="sm"
                            variant="light"
                            isIconOnly
                            onPress={() => {
                              setSelectedQueue(queue);
                              onShareOpen();
                            }}
                          >
                            <Share2 className="w-4 h-4" />
                          </Button>
                        </div>

                        {queue.description && (
                          <p className="text-sm text-default-600 mb-3 line-clamp-2">
                            {queue.description}
                          </p>
                        )}

                        <div className="flex items-center justify-between text-sm text-default-500 mb-3">
                          <span>{queue.learning_queue_items?.[0]?.count || 0} items</span>
                          <span>{queue.difficulty_level}</span>
                        </div>

                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            color="primary"
                            variant="flat"
                            className="flex-1"
                            onPress={() => duplicateQueue(queue)}
                          >
                            <Plus className="w-4 h-4 mr-1" />
                            Add to My Queues
                          </Button>
                          <Button
                            size="sm"
                            variant="light"
                            isIconOnly
                            onPress={() => viewSharedQueue(queue)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
              </div>

              {sharedQueues.length === 0 && (
                <div className="text-center py-12">
                  <Users className="w-16 h-16 mx-auto mb-4 text-default-400" />
                  <h3 className="text-xl font-semibold mb-2">No shared queues found</h3>
                  <p className="text-default-600">
                    Be the first to share a queue with the community!
                  </p>
                </div>
              )}
            </div>
          </Tab>
        </Tabs>
      </ModalBody>
          
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={onClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Create Queue Modal */}
      <Modal isOpen={isCreateOpen} onClose={onCreateClose} size="2xl">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">Create Learning Queue</h3>
          </ModalHeader>
          
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Queue Name"
                placeholder="e.g., Frontend Development Path"
                value={createForm.name}
                onChange={(e) => setCreateForm({ ...createForm, name: e.target.value })}
                isRequired
              />

              <Textarea
                label="Description (Optional)"
                placeholder="Describe what this learning queue covers..."
                value={createForm.description}
                onChange={(e) => setCreateForm({ ...createForm, description: e.target.value })}
                minRows={2}
              />

              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="Color"
                  type="color"
                  value={createForm.color}
                  onChange={(e) => setCreateForm({ ...createForm, color: e.target.value })}
                />

                <Select
                  label="Icon"
                  selectedKeys={[createForm.icon]}
                  onSelectionChange={(keys) => setCreateForm({ 
                    ...createForm, 
                    icon: Array.from(keys)[0] 
                  })}
                >
                  <SelectItem key="bi-collection-play" value="bi-collection-play">
                    📚 Collection
                  </SelectItem>
                  <SelectItem key="bi-mortarboard" value="bi-mortarboard">
                    🎓 Academic
                  </SelectItem>
                  <SelectItem key="bi-code-slash" value="bi-code-slash">
                    💻 Programming
                  </SelectItem>
                  <SelectItem key="bi-palette" value="bi-palette">
                    🎨 Design
                  </SelectItem>
                </Select>
              </div>

              <div className="space-y-2">
                <Checkbox
                  isSelected={createForm.isPublic}
                  onValueChange={(checked) => setCreateForm({ 
                    ...createForm, 
                    isPublic: checked 
                  })}
                >
                  Make this queue public (others can view and copy)
                </Checkbox>

                <Checkbox
                  isSelected={createForm.autoAdvance}
                  onValueChange={(checked) => setCreateForm({ 
                    ...createForm, 
                    autoAdvance: checked 
                  })}
                >
                  Auto-advance to next item when completed
                </Checkbox>
              </div>
            </div>
          </ModalBody>
          
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={onCreateClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleCreateQueue}
              isLoading={loading}
            >
              Create Queue
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Add Item Modal */}
      <Modal isOpen={isAddItemOpen} onClose={onAddItemClose} size="2xl">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">Add Item to Queue</h3>
          </ModalHeader>
          
          <ModalBody>
            <div className="space-y-4">
              <Select
                label="Item Type"
                selectedKeys={[addItemForm.itemType]}
                onSelectionChange={(keys) => setAddItemForm({ 
                  ...addItemForm, 
                  itemType: Array.from(keys)[0] 
                })}
              >
                <SelectItem key="video" value="video">📹 Video</SelectItem>
                <SelectItem key="course" value="course">📚 Course</SelectItem>
                <SelectItem key="article" value="article">📄 Article</SelectItem>
                <SelectItem key="project" value="project">🛠️ Project</SelectItem>
                <SelectItem key="assessment" value="assessment">📝 Assessment</SelectItem>
              </Select>

              <Input
                label="Title"
                placeholder="Enter item title"
                value={addItemForm.title}
                onChange={(e) => setAddItemForm({ ...addItemForm, title: e.target.value })}
                isRequired
              />

              <Input
                label="URL"
                placeholder="https://..."
                value={addItemForm.url}
                onChange={(e) => setAddItemForm({ ...addItemForm, url: e.target.value })}
              />

              <Textarea
                label="Description (Optional)"
                placeholder="Describe what this item covers..."
                value={addItemForm.description}
                onChange={(e) => setAddItemForm({ ...addItemForm, description: e.target.value })}
                minRows={2}
              />

              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="Duration (minutes)"
                  type="number"
                  placeholder="30"
                  value={addItemForm.durationMinutes}
                  onChange={(e) => setAddItemForm({ 
                    ...addItemForm, 
                    durationMinutes: e.target.value 
                  })}
                />

                <Select
                  label="Difficulty"
                  selectedKeys={[addItemForm.difficultyLevel]}
                  onSelectionChange={(keys) => setAddItemForm({ 
                    ...addItemForm, 
                    difficultyLevel: Array.from(keys)[0] 
                  })}
                >
                  <SelectItem key="beginner" value="beginner">Beginner</SelectItem>
                  <SelectItem key="intermediate" value="intermediate">Intermediate</SelectItem>
                  <SelectItem key="advanced" value="advanced">Advanced</SelectItem>
                </Select>
              </div>
            </div>
          </ModalBody>
          
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={onAddItemClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleAddItem}
              isLoading={loading}
            >
              Add Item
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Share Modal */}
      <ShareModal
        isOpen={isShareOpen}
        onClose={onShareClose}
        content={selectedQueue}
        contentType="learning_queue"
      />
    </>
  );
};

export default LearningQueue;
