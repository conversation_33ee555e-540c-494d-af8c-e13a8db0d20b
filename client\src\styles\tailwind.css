/* Import accessibility enhancements */
@import './accessibility.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Define CSS variables for light and dark themes */
:root {
  /* Light theme variables */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

.dark {
  /* Dark theme variables */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}

/* Ensure form inputs have proper text visibility in both themes */
input, textarea, select, [data-slot="input"] {
  color: hsl(var(--foreground)) !important;
  background-color: hsl(var(--background)) !important;
}

input::placeholder, textarea::placeholder, [data-slot="input"]::placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.8;
}

/* Specific fixes for white form backgrounds in dark mode */
.dark .bg-white input,
.dark .bg-white textarea,
.dark .bg-white select,
.dark .bg-white [data-slot="input"],
.dark [style*="background: white"] input,
.dark [style*="background: white"] textarea,
.dark [style*="background: white"] [data-slot="input"],
.dark [style*="background-color: white"] input,
.dark [style*="background-color: white"] textarea,
.dark [style*="background-color: white"] [data-slot="input"] {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
}

.dark .bg-white input::placeholder,
.dark .bg-white textarea::placeholder,
.dark .bg-white [data-slot="input"]::placeholder,
.dark [style*="background: white"] input::placeholder,
.dark [style*="background: white"] textarea::placeholder,
.dark [style*="background: white"] [data-slot="input"]::placeholder,
.dark [style*="background-color: white"] input::placeholder,
.dark [style*="background-color: white"] textarea::placeholder,
.dark [style*="background-color: white"] [data-slot="input"]::placeholder {
  color: #666666 !important;
  opacity: 1;
}

/* Clean slate - let HeroUI handle theming */

/* Fix for HeroUI Card components in dark mode */
.dark .bg-card {
  background: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
}

.dark .bg-card input,
.dark .bg-card textarea,
.dark .bg-card [data-slot="input"] {
  color: hsl(var(--card-foreground)) !important;
  background-color: hsl(var(--input)) !important;
}

/* Removed all CSS overrides - let HeroUI handle theming naturally */

/* Support for theme-aware input component */
.input-theme-aware [data-slot="input"] {
  color: inherit !important;
  -webkit-text-fill-color: inherit !important;
  caret-color: inherit !important;
}

.input-theme-aware.dark [data-slot="input"] {
  color: #ffffff !important;
  -webkit-text-fill-color: #ffffff !important;
  caret-color: #ffffff !important;
}

.input-theme-aware:not(.dark) [data-slot="input"] {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
  caret-color: #000000 !important;
}

/* Important text override class */
.important-text-override {
  color: inherit !important;
  -webkit-text-fill-color: inherit !important;
}

/* Let HeroUI handle dark mode properly - remove all overrides */

/* Let HeroUI handle all other styling */