-- Fix Column References Migration
-- Addresses missing column errors and ensures proper table structure

-- First, ensure profiles table has the required columns
DO $$ 
BEGIN
  -- Add display_name column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'display_name'
  ) THEN
    ALTER TABLE profiles ADD COLUMN display_name TEXT;
  END IF;

  -- Add full_name column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'full_name'
  ) THEN
    ALTER TABLE profiles ADD COLUMN full_name TEXT;
  END IF;

  -- Add avatar_url column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'avatar_url'
  ) THEN
    ALTER TABLE profiles ADD COLUMN avatar_url TEXT;
  END IF;

  -- Add bio column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'bio'
  ) THEN
    ALTER TABLE profiles ADD COLUMN bio TEXT;
  END IF;

  -- Add privacy settings columns if they don't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'allow_content_sharing'
  ) THEN
    ALTER TABLE profiles ADD COLUMN allow_content_sharing BOOLEAN DEFAULT true;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'allow_progress_sharing'
  ) THEN
    ALTER TABLE profiles ADD COLUMN allow_progress_sharing BOOLEAN DEFAULT true;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'public_profile'
  ) THEN
    ALTER TABLE profiles ADD COLUMN public_profile BOOLEAN DEFAULT true;
  END IF;

  -- Add social features columns if they don't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'follower_count'
  ) THEN
    ALTER TABLE profiles ADD COLUMN follower_count INTEGER DEFAULT 0;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'following_count'
  ) THEN
    ALTER TABLE profiles ADD COLUMN following_count INTEGER DEFAULT 0;
  END IF;
END $$;

-- Ensure learning_paths table has is_featured column
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'learning_paths' AND column_name = 'is_featured'
  ) THEN
    ALTER TABLE learning_paths ADD COLUMN is_featured BOOLEAN DEFAULT false;
  END IF;
END $$;

-- Recreate the sharing analytics view with safe column references
DROP VIEW IF EXISTS sharing_analytics;
CREATE OR REPLACE VIEW sharing_analytics AS
SELECT 
  cs.id,
  cs.shared_by,
  cs.content_type,
  cs.content_title,
  cs.is_public,
  cs.view_count,
  cs.like_count,
  cs.comment_count,
  cs.created_at,
  COALESCE(
    CASE WHEN p.display_name IS NOT NULL AND p.display_name != '' THEN p.display_name
         WHEN p.full_name IS NOT NULL AND p.full_name != '' THEN p.full_name
         ELSE 'Unknown User'
    END
  ) as sharer_name,
  p.avatar_url as sharer_avatar,
  CASE 
    WHEN cs.view_count > 0 THEN (cs.like_count::DECIMAL / cs.view_count) * 100
    ELSE 0 
  END as engagement_rate,
  array_length(cs.recipients, 1) as direct_share_count
FROM content_shares cs
LEFT JOIN profiles p ON p.id = cs.shared_by
WHERE cs.is_public = true OR cs.shared_by = auth.uid()
ORDER BY cs.created_at DESC;

-- Create indexes safely (only if they don't exist)
DO $$ 
BEGIN
  -- Learning paths indexes
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE tablename = 'learning_paths' AND indexname = 'idx_learning_paths_active'
  ) THEN
    CREATE INDEX idx_learning_paths_active ON learning_paths(is_active) WHERE is_active = true;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE tablename = 'learning_paths' AND indexname = 'idx_learning_paths_featured'
  ) THEN
    CREATE INDEX idx_learning_paths_featured ON learning_paths(is_featured) WHERE is_featured = true;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE tablename = 'learning_paths' AND indexname = 'idx_learning_paths_difficulty'
  ) THEN
    CREATE INDEX idx_learning_paths_difficulty ON learning_paths(difficulty_level);
  END IF;

  -- Content shares indexes
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE tablename = 'content_shares' AND indexname = 'idx_content_shares_shared_by'
  ) THEN
    CREATE INDEX idx_content_shares_shared_by ON content_shares(shared_by);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE tablename = 'content_shares' AND indexname = 'idx_content_shares_content_type'
  ) THEN
    CREATE INDEX idx_content_shares_content_type ON content_shares(content_type);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE tablename = 'content_shares' AND indexname = 'idx_content_shares_public'
  ) THEN
    CREATE INDEX idx_content_shares_public ON content_shares(is_public) WHERE is_public = true;
  END IF;
END $$;

-- Update existing profiles to have display_name if they don't
UPDATE profiles 
SET display_name = COALESCE(full_name, email, 'User')
WHERE display_name IS NULL OR display_name = '';

-- Grant permissions on the updated view
GRANT SELECT ON sharing_analytics TO authenticated;

-- Comments
COMMENT ON VIEW sharing_analytics IS 'Fixed analytics view for content sharing performance with safe column references';
COMMENT ON COLUMN profiles.display_name IS 'User display name for public visibility';
COMMENT ON COLUMN profiles.full_name IS 'User full legal name';
COMMENT ON COLUMN profiles.avatar_url IS 'URL to user profile picture';
COMMENT ON COLUMN profiles.bio IS 'User biography or description';
COMMENT ON COLUMN profiles.allow_content_sharing IS 'Whether user allows content to be shared with them';
COMMENT ON COLUMN profiles.allow_progress_sharing IS 'Whether user allows progress to be shared';
COMMENT ON COLUMN profiles.public_profile IS 'Whether user profile is publicly visible';
COMMENT ON COLUMN profiles.follower_count IS 'Number of users following this user';
COMMENT ON COLUMN profiles.following_count IS 'Number of users this user is following';
