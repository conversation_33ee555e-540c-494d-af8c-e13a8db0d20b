-- Create Core Tables Required by Other Migrations
-- This creates the essential tables that other migrations depend on

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- PROJECTS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'on_hold', 'completed', 'cancelled', 'archived')),
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Additional columns that may be referenced
    project_type TEXT DEFAULT 'software',
    team_id UUID,
    studio_id UUID,
    alliance_id UUID,
    is_public BOOLEAN DEFAULT false,
    budget DECIMAL(12,2),
    start_date DATE,
    end_date DATE
);

-- ============================================================================
-- TASKS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'review', 'done', 'cancelled')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    assignee_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    due_date TIMESTAMP WITH TIME ZONE,
    estimated_hours INTEGER,
    logged_hours INTEGER DEFAULT 0,
    
    -- Mission/bounty specific columns
    task_category TEXT DEFAULT 'task' CHECK (task_category IN ('mission', 'bounty', 'task')),
    mission_type TEXT CHECK (mission_type IN ('skill', 'collaboration', 'achievement', 'exploration', 'social')),
    mission_requirements JSONB,
    mission_rewards JSONB
);

-- ============================================================================
-- COLLABORATION REQUESTS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.collaboration_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id UUID REFERENCES auth.users(id) NOT NULL,
    target_user_id UUID REFERENCES auth.users(id),
    target_audience VARCHAR(50) DEFAULT 'specific' CHECK (target_audience IN ('specific', 'network', 'public')),
    project_title VARCHAR(255) NOT NULL,
    project_description TEXT NOT NULL,
    required_skills TEXT[], -- Array of required skills
    budget_range_min INTEGER,
    budget_range_max INTEGER,
    timeline_weeks INTEGER,
    project_type VARCHAR(50) CHECK (project_type IN ('fixed', 'ongoing', 'hourly')),
    experience_level VARCHAR(50) CHECK (experience_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    deadline DATE,
    location TEXT,
    is_remote BOOLEAN DEFAULT true
);

-- ============================================================================
-- COLLABORATION REQUEST APPLICATIONS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.collaboration_request_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    collaboration_request_id UUID REFERENCES public.collaboration_requests(id) ON DELETE CASCADE,
    applicant_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    application_message TEXT,
    proposed_budget DECIMAL(10,2),
    proposed_timeline_weeks INTEGER,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'withdrawn')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    UNIQUE(collaboration_request_id, applicant_id)
);

-- ============================================================================
-- TEAMS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.teams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    is_active BOOLEAN DEFAULT true
);

-- ============================================================================
-- TEAM MEMBERS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.team_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    team_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    UNIQUE(team_id, user_id)
);

-- ============================================================================
-- PROJECT CONTRIBUTORS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.project_contributors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT DEFAULT 'contributor' CHECK (role IN ('owner', 'admin', 'contributor', 'viewer')),
    contribution_percentage DECIMAL(5,2) DEFAULT 0.00,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    UNIQUE(project_id, user_id)
);

-- ============================================================================
-- INDEXES
-- ============================================================================
CREATE INDEX IF NOT EXISTS idx_projects_created_by ON public.projects(created_by);
CREATE INDEX IF NOT EXISTS idx_projects_team_id ON public.projects(team_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);

CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON public.tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_tasks_assignee_id ON public.tasks(assignee_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON public.tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_created_by ON public.tasks(created_by);

CREATE INDEX IF NOT EXISTS idx_collaboration_requests_requester_id ON public.collaboration_requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_collaboration_requests_status ON public.collaboration_requests(status);

CREATE INDEX IF NOT EXISTS idx_team_members_team_id ON public.team_members(team_id);
CREATE INDEX IF NOT EXISTS idx_team_members_user_id ON public.team_members(user_id);

CREATE INDEX IF NOT EXISTS idx_project_contributors_project_id ON public.project_contributors(project_id);
CREATE INDEX IF NOT EXISTS idx_project_contributors_user_id ON public.project_contributors(user_id);

-- ============================================================================
-- ROW LEVEL SECURITY
-- ============================================================================
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collaboration_request_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_contributors ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be overridden by later migrations)
CREATE POLICY "Users can view public projects" ON public.projects FOR SELECT USING (is_public = true);
CREATE POLICY "Users can manage their own projects" ON public.projects FOR ALL USING (created_by = auth.uid());

CREATE POLICY "Users can view tasks in projects they have access to" ON public.tasks FOR SELECT USING (
    project_id IN (
        SELECT id FROM public.projects 
        WHERE created_by = auth.uid() OR is_public = true
    )
);

CREATE POLICY "Users can manage their own collaboration requests" ON public.collaboration_requests FOR ALL USING (requester_id = auth.uid());
CREATE POLICY "Users can view public collaboration requests" ON public.collaboration_requests FOR SELECT USING (target_audience = 'public');

COMMENT ON TABLE public.projects IS 'Core projects table for all project management functionality';
COMMENT ON TABLE public.tasks IS 'Tasks, missions, and bounties within projects';
COMMENT ON TABLE public.collaboration_requests IS 'Gig requests and collaboration opportunities';
COMMENT ON TABLE public.teams IS 'Teams and studios for organizing users';
