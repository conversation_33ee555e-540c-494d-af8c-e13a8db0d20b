-- Video Voting System Migration
-- Adds voting functionality for video recommendations and admin vetting interface

-- Note: Using text values in CASE statement to avoid enum transaction issues

-- Create video recommendation votes table
CREATE TABLE IF NOT EXISTS video_recommendation_votes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Vote relationship
  recommendation_id UUID NOT NULL REFERENCES video_recommendations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Vote details
  vote_type TEXT NOT NULL CHECK (vote_type IN ('upvote', 'downvote')),
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one vote per user per recommendation
  UNIQUE(recommendation_id, user_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_video_recommendation_votes_recommendation_id ON video_recommendation_votes(recommendation_id);
CREATE INDEX IF NOT EXISTS idx_video_recommendation_votes_user_id ON video_recommendation_votes(user_id);

-- <PERSON><PERSON> function to update vote counts
CREATE OR REPLACE FUNCTION update_recommendation_vote_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update upvotes and downvotes count in video_recommendations table
  UPDATE video_recommendations 
  SET 
    upvotes = (
      SELECT COUNT(*) 
      FROM video_recommendation_votes 
      WHERE recommendation_id = COALESCE(NEW.recommendation_id, OLD.recommendation_id) 
      AND vote_type = 'upvote'
    ),
    downvotes = (
      SELECT COUNT(*) 
      FROM video_recommendation_votes 
      WHERE recommendation_id = COALESCE(NEW.recommendation_id, OLD.recommendation_id) 
      AND vote_type = 'downvote'
    )
  WHERE id = COALESCE(NEW.recommendation_id, OLD.recommendation_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update vote counts
DROP TRIGGER IF EXISTS trigger_update_recommendation_vote_counts ON video_recommendation_votes;
CREATE TRIGGER trigger_update_recommendation_vote_counts
  AFTER INSERT OR UPDATE OR DELETE ON video_recommendation_votes
  FOR EACH ROW
  EXECUTE FUNCTION update_recommendation_vote_counts();

-- Add admin vetting fields to video_submissions if they don't exist
ALTER TABLE video_submissions 
ADD COLUMN IF NOT EXISTS admin_priority INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS community_score DECIMAL(3,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS auto_approve_threshold INTEGER DEFAULT 10,
ADD COLUMN IF NOT EXISTS training_track TEXT,
ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS allow_voting BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS thumbnail_url TEXT;

-- Create admin video review queue view
CREATE OR REPLACE VIEW admin_video_review_queue AS
SELECT 
  vs.*,
  vr.upvotes,
  vr.downvotes,
  (vr.upvotes - vr.downvotes) as net_votes,
  CASE
    WHEN vs.status = 'pending' AND (vr.upvotes - vr.downvotes) >= vs.auto_approve_threshold THEN 'auto_approve_ready'::text
    WHEN vs.status = 'pending' AND vs.admin_priority > 0 THEN 'high_priority'::text
    WHEN vs.status = 'pending' THEN 'normal'::text
    ELSE vs.status::text
  END as review_priority,
  u.email as submitter_email,
  u.user_metadata->>'full_name' as submitter_full_name
FROM video_submissions vs
LEFT JOIN video_recommendations vr ON vr.video_url = vs.video_url
LEFT JOIN auth.users u ON u.id = vs.submitted_by
ORDER BY 
  CASE 
    WHEN vs.status = 'pending' AND (vr.upvotes - vr.downvotes) >= vs.auto_approve_threshold THEN 1
    WHEN vs.status = 'pending' AND vs.admin_priority > 0 THEN 2
    WHEN vs.status = 'pending' THEN 3
    ELSE 4
  END,
  vs.created_at DESC;

-- Create learning content analytics view
CREATE OR REPLACE VIEW learning_content_analytics AS
SELECT 
  cc.id,
  cc.title,
  cc.provider,
  cc.external_id,
  cc.difficulty_level,
  cc.skills,
  cc.categories,
  cc.created_at,
  COUNT(DISTINCT lp.user_id) as total_enrollments,
  COUNT(DISTINCT CASE WHEN lp.status = 'completed' THEN lp.user_id END) as completions,
  AVG(lp.completion_percentage) as avg_progress,
  COUNT(DISTINCT CASE WHEN lp.completion_percentage > 0 THEN lp.user_id END) as active_learners,
  CASE 
    WHEN COUNT(DISTINCT lp.user_id) > 0 
    THEN (COUNT(DISTINCT CASE WHEN lp.status = 'completed' THEN lp.user_id END)::DECIMAL / COUNT(DISTINCT lp.user_id)) * 100
    ELSE 0 
  END as completion_rate
FROM course_catalog cc
LEFT JOIN learning_progress lp ON lp.course_id = cc.external_id AND lp.course_provider = cc.provider
WHERE cc.is_active = true
GROUP BY cc.id, cc.title, cc.provider, cc.external_id, cc.difficulty_level, cc.skills, cc.categories, cc.created_at;

-- Add RLS policies for video recommendation votes
ALTER TABLE video_recommendation_votes ENABLE ROW LEVEL SECURITY;

-- Users can view all votes
CREATE POLICY "Users can view video recommendation votes" ON video_recommendation_votes
  FOR SELECT USING (true);

-- Users can insert their own votes
CREATE POLICY "Users can insert their own votes" ON video_recommendation_votes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own votes
CREATE POLICY "Users can update their own votes" ON video_recommendation_votes
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own votes
CREATE POLICY "Users can delete their own votes" ON video_recommendation_votes
  FOR DELETE USING (auth.uid() = user_id);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON video_recommendation_votes TO authenticated;
GRANT SELECT ON admin_video_review_queue TO authenticated;
GRANT SELECT ON learning_content_analytics TO authenticated;

-- Comments
COMMENT ON TABLE video_recommendation_votes IS 'Stores user votes on video recommendations for community-driven content curation';
COMMENT ON VIEW admin_video_review_queue IS 'Prioritized queue of video submissions for admin review with community voting data';
COMMENT ON VIEW learning_content_analytics IS 'Analytics view for learning content performance and engagement metrics';
