-- Fix Dashboard Query Issues
-- This migration fixes all the dashboard-related database errors

-- ============================================================================
-- 1. FIX TEAM_MEMBERS POLICIES (500 ERRORS)
-- ============================================================================

-- The current policies are causing infinite recursion or access issues
-- Let's create ultra-simple policies that work

-- Drop all existing team_members policies
DROP POLICY IF EXISTS "team_members_select_accessible" ON public.team_members;
DROP POLICY IF EXISTS "team_members_insert_admin" ON public.team_members;
DROP POLICY IF EXISTS "team_members_update_admin" ON public.team_members;
DROP POLICY IF EXISTS "team_members_delete_admin" ON public.team_members;

-- Create simple, non-recursive policies
CREATE POLICY "team_members_select_simple" ON public.team_members
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "team_members_insert_simple" ON public.team_members
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "team_members_update_simple" ON public.team_members
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "team_members_delete_simple" ON public.team_members
    FOR DELETE USING (user_id = auth.uid());

-- ============================================================================
-- 2. CREATE VIEWS FOR COMPLEX DASHBOARD QUERIES
-- ============================================================================

-- Create a view for user's accessible projects (fixes 400 errors)
CREATE OR REPLACE VIEW user_dashboard_projects AS
SELECT DISTINCT
    p.id,
    p.name,
    p.title,
    p.description,
    p.status,
    p.created_by,
    p.team_id,
    p.created_at,
    p.updated_at,
    p.is_active,
    CASE 
        WHEN p.created_by = auth.uid() THEN 'owner'
        ELSE 'contributor'
    END as user_role
FROM public.projects p
WHERE p.created_by = auth.uid()
   OR p.id IN (
       SELECT project_id FROM public.project_contributors 
       WHERE user_id = auth.uid()
   );

-- Create a view for user's team memberships (fixes 500 errors)
CREATE OR REPLACE VIEW user_team_memberships AS
SELECT 
    tm.id,
    tm.team_id,
    tm.user_id,
    tm.role,
    tm.status,
    tm.is_admin,
    tm.joined_at,
    t.name as team_name,
    t.description as team_description,
    t.studio_type,
    t.industry
FROM public.team_members tm
JOIN public.teams t ON tm.team_id = t.id
WHERE tm.user_id = auth.uid();

-- Create a view for recent user activity
CREATE OR REPLACE VIEW user_recent_activity AS
SELECT 
    'project' as activity_type,
    p.id as item_id,
    p.name as item_name,
    p.updated_at as activity_date,
    'Updated project' as activity_description
FROM public.projects p
WHERE p.created_by = auth.uid()
UNION ALL
SELECT 
    'contribution' as activity_type,
    c.id as item_id,
    p.name as item_name,
    c.updated_at as activity_date,
    'Made contribution' as activity_description
FROM public.contributions c
JOIN public.projects p ON c.project_id = p.id
WHERE c.user_id = auth.uid()
ORDER BY activity_date DESC
LIMIT 10;

-- ============================================================================
-- 3. CREATE FUNCTIONS FOR DASHBOARD ANALYTICS
-- ============================================================================

-- Function to get user's dashboard stats
CREATE OR REPLACE FUNCTION get_user_dashboard_stats(user_uuid UUID)
RETURNS TABLE(
    total_projects INTEGER,
    active_projects INTEGER,
    total_teams INTEGER,
    total_contributions INTEGER,
    recent_activity_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*)::INTEGER FROM public.projects WHERE created_by = user_uuid) as total_projects,
        (SELECT COUNT(*)::INTEGER FROM public.projects WHERE created_by = user_uuid AND is_active = true) as active_projects,
        (SELECT COUNT(*)::INTEGER FROM public.team_members WHERE user_id = user_uuid AND status = 'active') as total_teams,
        (SELECT COUNT(*)::INTEGER FROM public.contributions WHERE user_id = user_uuid) as total_contributions,
        (SELECT COUNT(*)::INTEGER FROM user_recent_activity) as recent_activity_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's recent projects
CREATE OR REPLACE FUNCTION get_user_recent_projects(user_uuid UUID, project_limit INTEGER DEFAULT 5)
RETURNS TABLE(
    id UUID,
    name TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    status TEXT,
    user_role TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.created_at,
        p.updated_at,
        p.status,
        CASE 
            WHEN p.created_by = user_uuid THEN 'owner'
            ELSE 'contributor'
        END as user_role
    FROM public.projects p
    WHERE p.created_by = user_uuid
       OR p.id IN (
           SELECT project_id FROM public.project_contributors 
           WHERE user_id = user_uuid
       )
    ORDER BY p.updated_at DESC
    LIMIT project_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 4. GRANT PERMISSIONS ON VIEWS AND FUNCTIONS
-- ============================================================================

-- Grant permissions on views
GRANT SELECT ON user_dashboard_projects TO authenticated;
GRANT SELECT ON user_team_memberships TO authenticated;
GRANT SELECT ON user_recent_activity TO authenticated;

-- Grant permissions on functions
GRANT EXECUTE ON FUNCTION get_user_dashboard_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_recent_projects(UUID, INTEGER) TO authenticated;

-- ============================================================================
-- 5. CREATE SIMPLE ANALYTICS TABLE FOR DASHBOARD
-- ============================================================================

-- Create a simple analytics table to avoid complex queries
CREATE TABLE IF NOT EXISTS public.user_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    metric_name TEXT NOT NULL,
    metric_value INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    UNIQUE(user_id, metric_name)
);

-- Enable RLS on analytics table
ALTER TABLE public.user_analytics ENABLE ROW LEVEL SECURITY;

-- Create policy for analytics
CREATE POLICY "user_analytics_select_own" ON public.user_analytics
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "user_analytics_insert_own" ON public.user_analytics
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "user_analytics_update_own" ON public.user_analytics
    FOR UPDATE USING (user_id = auth.uid());

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.user_analytics TO authenticated;

-- ============================================================================
-- 6. CREATE FUNCTION TO UPDATE USER ANALYTICS
-- ============================================================================

-- Function to refresh user analytics
CREATE OR REPLACE FUNCTION refresh_user_analytics(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    -- Insert or update project count
    INSERT INTO public.user_analytics (user_id, metric_name, metric_value, last_updated)
    VALUES (
        user_uuid, 
        'total_projects', 
        (SELECT COUNT(*) FROM public.projects WHERE created_by = user_uuid)::INTEGER,
        now()
    )
    ON CONFLICT (user_id, metric_name) 
    DO UPDATE SET 
        metric_value = EXCLUDED.metric_value,
        last_updated = EXCLUDED.last_updated;

    -- Insert or update team count
    INSERT INTO public.user_analytics (user_id, metric_name, metric_value, last_updated)
    VALUES (
        user_uuid, 
        'total_teams', 
        (SELECT COUNT(*) FROM public.team_members WHERE user_id = user_uuid AND status = 'active')::INTEGER,
        now()
    )
    ON CONFLICT (user_id, metric_name) 
    DO UPDATE SET 
        metric_value = EXCLUDED.metric_value,
        last_updated = EXCLUDED.last_updated;

    -- Insert or update contribution count
    INSERT INTO public.user_analytics (user_id, metric_name, metric_value, last_updated)
    VALUES (
        user_uuid, 
        'total_contributions', 
        (SELECT COUNT(*) FROM public.contributions WHERE user_id = user_uuid)::INTEGER,
        now()
    )
    ON CONFLICT (user_id, metric_name) 
    DO UPDATE SET 
        metric_value = EXCLUDED.metric_value,
        last_updated = EXCLUDED.last_updated;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permission on refresh function
GRANT EXECUTE ON FUNCTION refresh_user_analytics(UUID) TO authenticated;

-- ============================================================================
-- 7. INITIALIZE ANALYTICS FOR CURRENT USER
-- ============================================================================

-- Initialize analytics for the authenticated user
SELECT refresh_user_analytics(auth.uid()) WHERE auth.uid() IS NOT NULL;

-- ============================================================================
-- 8. VERIFICATION QUERIES
-- ============================================================================

-- Test that we can query team memberships without errors
SELECT 'Testing team memberships view' as test_name;
SELECT COUNT(*) as membership_count FROM user_team_memberships;

-- Test that we can query dashboard projects without errors
SELECT 'Testing dashboard projects view' as test_name;
SELECT COUNT(*) as project_count FROM user_dashboard_projects;

-- Test analytics function
SELECT 'Testing dashboard stats function' as test_name;
SELECT * FROM get_user_dashboard_stats(auth.uid()) WHERE auth.uid() IS NOT NULL;

-- ============================================================================
-- 9. FINAL STATUS
-- ============================================================================

SELECT '📊 DASHBOARD QUERY FIXES APPLIED' as status;
SELECT 'Fixed team_members 500 errors with simple policies' as fix_1;
SELECT 'Created views for complex dashboard queries' as fix_2;
SELECT 'Added analytics functions for dashboard stats' as fix_3;
SELECT 'Created user analytics table for performance' as fix_4;
SELECT 'Dashboard should now load without errors' as fix_5;
