-- Fix Activity and Learning System Issues
-- This migration creates missing tables and populates them with sample data

-- Create user_activity_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    
    -- Event categorization
    event_type TEXT NOT NULL, -- 'navigation', 'interaction', 'error', 'debug'
    event_category TEXT NOT NULL, -- 'page_view', 'click', 'hover', 'drag', 'keyboard', etc.
    event_action TEXT NOT NULL, -- specific action taken
    
    -- Navigation specific
    from_page TEXT,
    to_page TEXT,
    navigation_method TEXT, -- 'click', 'keyboard', 'url', 'back_button'
    
    -- Additional data
    metadata JSONB DEFAULT '{}'::jsonb,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create project_activities table if it doesn't exist
CREATE TABLE IF NOT EXISTS project_activities (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL,
    activity_data JSONB DEFAULT '{}'::jsonb,
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create learning_queues table if it doesn't exist
CREATE TABLE IF NOT EXISTS learning_queues (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    color TEXT DEFAULT '#3b82f6',
    icon TEXT DEFAULT 'bi-collection-play',
    is_public BOOLEAN DEFAULT false,
    auto_advance BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create learning_queue_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS learning_queue_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    queue_id UUID REFERENCES learning_queues(id) ON DELETE CASCADE,
    item_type TEXT NOT NULL CHECK (item_type IN ('video', 'course', 'article', 'project', 'assessment')),
    external_id TEXT,
    title TEXT NOT NULL,
    description TEXT,
    url TEXT,
    provider TEXT,
    duration_minutes INTEGER,
    difficulty_level TEXT DEFAULT 'medium' CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
    skills TEXT[],
    sequence_order INTEGER DEFAULT 1,
    thumbnail_url TEXT,
    status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'skipped')),
    progress_percentage INTEGER DEFAULT 0,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create learning_content table if it doesn't exist
CREATE TABLE IF NOT EXISTS learning_content (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_type TEXT NOT NULL CHECK (content_type IN ('tutorial', 'best_practice', 'documentation', 'video', 'article')),
    title TEXT NOT NULL,
    description TEXT,
    content_body TEXT,
    difficulty_level TEXT DEFAULT 'medium' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    category TEXT,
    tags TEXT[],
    vote_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create video_recommendations table if it doesn't exist
CREATE TABLE IF NOT EXISTS video_recommendations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    video_url TEXT NOT NULL,
    thumbnail_url TEXT,
    provider TEXT DEFAULT 'youtube',
    duration_minutes INTEGER,
    category TEXT,
    skills TEXT[],
    difficulty_level TEXT DEFAULT 'medium',
    recommended_by UUID REFERENCES auth.users(id),
    recommended_by_name TEXT,
    upvotes INTEGER DEFAULT 0,
    downvotes INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT true,
    is_vetted BOOLEAN DEFAULT false,
    vetted_by UUID REFERENCES auth.users(id),
    vetted_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user_learning_progress table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_learning_progress (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content_id UUID REFERENCES learning_content(id) ON DELETE CASCADE,
    progress_percentage INTEGER DEFAULT 0,
    status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
    time_spent_minutes INTEGER DEFAULT 0,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    last_accessed_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, content_id)
);

-- Create content_votes table if it doesn't exist
CREATE TABLE IF NOT EXISTS content_votes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content_id UUID REFERENCES learning_content(id) ON DELETE CASCADE,
    vote_type TEXT CHECK (vote_type IN ('upvote', 'downvote')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, content_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_timestamp ON user_activity_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_project_activities_project_id ON project_activities(project_id);
CREATE INDEX IF NOT EXISTS idx_project_activities_user_id ON project_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_queues_user_id ON learning_queues(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_queue_items_queue_id ON learning_queue_items(queue_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_type ON learning_content(content_type);
CREATE INDEX IF NOT EXISTS idx_video_recommendations_public ON video_recommendations(is_public) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_user_id ON user_learning_progress(user_id);

-- Enable RLS
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_queues ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_queue_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_learning_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_votes ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own activity logs" ON user_activity_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activity logs" ON user_activity_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view project activities for their projects" ON project_activities
    FOR SELECT USING (
        project_id IN (SELECT id FROM projects WHERE created_by = auth.uid()) OR
        user_id = auth.uid() OR
        is_public = true
    );

CREATE POLICY "Users can create project activities" ON project_activities
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage their own learning queues" ON learning_queues
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view public learning queues" ON learning_queues
    FOR SELECT USING (is_public = true OR auth.uid() = user_id);

CREATE POLICY "Users can manage items in their queues" ON learning_queue_items
    FOR ALL USING (
        queue_id IN (SELECT id FROM learning_queues WHERE user_id = auth.uid())
    );

CREATE POLICY "Anyone can view active learning content" ON learning_content
    FOR SELECT USING (is_active = true);

CREATE POLICY "Users can create learning content" ON learning_content
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Anyone can view public video recommendations" ON video_recommendations
    FOR SELECT USING (is_public = true);

CREATE POLICY "Users can create video recommendations" ON video_recommendations
    FOR INSERT WITH CHECK (auth.uid() = recommended_by);

CREATE POLICY "Users can view their own learning progress" ON user_learning_progress
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own content votes" ON content_votes
    FOR ALL USING (auth.uid() = user_id);

-- Function to populate sample data for a user
CREATE OR REPLACE FUNCTION populate_sample_data_for_user(p_user_id UUID)
RETURNS VOID AS $$
BEGIN
    -- Insert sample recent activities
    INSERT INTO user_activity_logs (user_id, session_id, event_type, event_category, event_action, from_page, to_page, navigation_method)
    VALUES 
        (p_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_dashboard', null, '/dashboard', 'direct'),
        (p_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_projects', '/dashboard', '/projects', 'click'),
        (p_user_id, 'sample_session_1', 'interaction', 'click', 'created_project', '/projects', '/projects', 'click'),
        (p_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_learning', '/projects', '/learn', 'click'),
        (p_user_id, 'sample_session_2', 'navigation', 'page_view', 'visited_track', '/learn', '/track', 'click')
    ON CONFLICT DO NOTHING;

    -- Insert sample learning content
    INSERT INTO learning_content (content_type, title, description, difficulty_level, category, is_active, created_by)
    VALUES 
        ('tutorial', 'Getting Started with React', 'Learn the basics of React development', 'beginner', 'Development', true, p_user_id),
        ('best_practice', 'Clean Code Principles', 'Best practices for writing maintainable code', 'intermediate', 'Development', true, p_user_id),
        ('documentation', 'API Documentation Guide', 'How to write effective API documentation', 'intermediate', 'Documentation', true, p_user_id)
    ON CONFLICT DO NOTHING;

    -- Insert sample video recommendations
    INSERT INTO video_recommendations (title, description, video_url, provider, category, recommended_by, recommended_by_name, is_public)
    VALUES 
        ('React Hooks Explained', 'Comprehensive guide to React Hooks', 'https://youtube.com/watch?v=example1', 'youtube', 'Development', p_user_id, 'Sample User', true),
        ('JavaScript ES6 Features', 'Modern JavaScript features you should know', 'https://youtube.com/watch?v=example2', 'youtube', 'Development', p_user_id, 'Sample User', true),
        ('CSS Grid Layout', 'Master CSS Grid for modern layouts', 'https://youtube.com/watch?v=example3', 'youtube', 'Design', p_user_id, 'Sample User', true)
    ON CONFLICT DO NOTHING;

    -- Insert sample learning queue
    INSERT INTO learning_queues (user_id, name, description, color, icon)
    VALUES 
        (p_user_id, 'Frontend Development Path', 'Complete frontend development learning journey', '#3b82f6', 'bi-code-slash'),
        (p_user_id, 'Backend Fundamentals', 'Learn backend development basics', '#10b981', 'bi-server'),
        (p_user_id, 'DevOps Essentials', 'Essential DevOps tools and practices', '#f59e0b', 'bi-gear')
    ON CONFLICT DO NOTHING;

    -- Insert sample queue items
    INSERT INTO learning_queue_items (queue_id, item_type, title, description, url, provider, duration_minutes, difficulty_level, sequence_order)
    SELECT 
        q.id,
        'video',
        'Introduction to ' || q.name,
        'Getting started with ' || q.description,
        'https://example.com/video/' || q.id,
        'youtube',
        30,
        'easy',
        1
    FROM learning_queues q 
    WHERE q.user_id = p_user_id
    ON CONFLICT DO NOTHING;

END;
$$ LANGUAGE plpgsql;

-- Function to create sample project activities
CREATE OR REPLACE FUNCTION create_sample_project_activities(p_user_id UUID)
RETURNS VOID AS $$
DECLARE
    v_project_id UUID;
BEGIN
    -- Get a project for this user
    SELECT id INTO v_project_id
    FROM projects 
    WHERE created_by = p_user_id 
    LIMIT 1;
    
    -- If no project exists, create one
    IF v_project_id IS NULL THEN
        INSERT INTO projects (id, name, description, status, created_by)
        VALUES (
            uuid_generate_v4(),
            'Sample Project',
            'A sample project for demonstration',
            'active',
            p_user_id
        )
        RETURNING id INTO v_project_id;
    END IF;
    
    -- Insert sample project activities
    INSERT INTO project_activities (project_id, user_id, activity_type, activity_data)
    VALUES 
        (v_project_id, p_user_id, 'project_created', '{"action": "Created new project"}'),
        (v_project_id, p_user_id, 'task_completed', '{"task": "Setup development environment", "hours": 2}'),
        (v_project_id, p_user_id, 'milestone_reached', '{"milestone": "Initial setup complete"}'),
        (v_project_id, p_user_id, 'collaboration_started', '{"action": "Invited team member"}')
    ON CONFLICT DO NOTHING;
    
END;
$$ LANGUAGE plpgsql;

-- Comments
COMMENT ON TABLE user_activity_logs IS 'Tracks user navigation and interaction activities';
COMMENT ON TABLE project_activities IS 'Tracks activities within projects for recent activity feeds';
COMMENT ON TABLE learning_queues IS 'User-created learning queues for organizing educational content';
COMMENT ON TABLE learning_queue_items IS 'Individual items within learning queues';
COMMENT ON TABLE learning_content IS 'Educational content including tutorials, best practices, and documentation';
COMMENT ON TABLE video_recommendations IS 'Community-recommended educational videos';
COMMENT ON FUNCTION populate_sample_data_for_user IS 'Populates sample learning and activity data for a user';
COMMENT ON FUNCTION create_sample_project_activities IS 'Creates sample project activities for recent activity display';
