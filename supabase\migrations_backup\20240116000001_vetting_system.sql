-- Vetting System Database Schema
-- Migration: 20240116000001_vetting_system.sql

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Vetting status enum
DO $$ BEGIN
    CREATE TYPE vetting_status AS ENUM (
      'pending',
      'under_review',
      'approved',
      'rejected',
      'appealing',
      'suspended',
      'expired'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Vetting application types
DO $$ BEGIN
    CREATE TYPE application_type AS ENUM (
      'developer',
      'designer',
      'project_manager',
      'qa_tester',
      'business_analyst',
      'other'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Review decision enum
DO $$ BEGIN
    CREATE TYPE review_decision AS ENUM (
      'approve',
      'reject',
      'request_more_info',
      'escalate'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Skill verification levels
DO $$ BEGIN
    CREATE TYPE verification_level AS ENUM (
      'unverified',
      'self_reported',
      'peer_verified',
      'expert_verified',
      'industry_certified'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Vetting applications table
CREATE TABLE IF NOT EXISTS vetting_applications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  application_type application_type NOT NULL,
  status vetting_status DEFAULT 'pending',
  
  -- Application data
  portfolio_url TEXT,
  linkedin_profile TEXT,
  github_profile TEXT,
  years_experience INTEGER,
  hourly_rate DECIMAL(10,2),
  availability_hours INTEGER,
  timezone TEXT,
  
  -- Skills and expertise
  primary_skills JSONB DEFAULT '[]'::jsonb,
  secondary_skills JSONB DEFAULT '[]'::jsonb,
  certifications JSONB DEFAULT '[]'::jsonb,
  
  -- Application content
  motivation_statement TEXT,
  project_examples JSONB DEFAULT '[]'::jsonb,
  professional_references JSONB DEFAULT '[]'::jsonb,
  
  -- Workflow tracking
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  approved_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  
  -- Metadata
  application_data JSONB DEFAULT '{}'::jsonb,
  internal_notes TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vetting criteria table
CREATE TABLE IF NOT EXISTS vetting_criteria (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  application_type application_type NOT NULL,
  criterion_name TEXT NOT NULL,
  criterion_description TEXT,
  weight DECIMAL(3,2) DEFAULT 1.0,
  min_score DECIMAL(3,2) DEFAULT 0.0,
  max_score DECIMAL(3,2) DEFAULT 10.0,
  is_required BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vetting reviews table
CREATE TABLE IF NOT EXISTS vetting_reviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  application_id UUID NOT NULL REFERENCES vetting_applications(id) ON DELETE CASCADE,
  reviewer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Review data
  decision review_decision NOT NULL,
  overall_score DECIMAL(4,2),
  criteria_scores JSONB DEFAULT '{}'::jsonb,
  
  -- Review content
  strengths TEXT,
  weaknesses TEXT,
  recommendations TEXT,
  internal_notes TEXT,
  
  -- Workflow
  review_started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  review_completed_at TIMESTAMP WITH TIME ZONE,
  is_final_review BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reviewer assignments table
CREATE TABLE IF NOT EXISTS reviewer_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  application_id UUID NOT NULL REFERENCES vetting_applications(id) ON DELETE CASCADE,
  reviewer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  assigned_by UUID REFERENCES auth.users(id),
  due_date TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  
  -- Assignment metadata
  assignment_type TEXT DEFAULT 'primary', -- primary, secondary, escalated
  priority INTEGER DEFAULT 1,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(application_id, reviewer_id)
);

-- Skill assessments table
CREATE TABLE IF NOT EXISTS skill_assessments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  application_id UUID REFERENCES vetting_applications(id) ON DELETE CASCADE,
  
  skill_name TEXT NOT NULL,
  skill_category TEXT,
  assessment_type TEXT, -- 'self_reported', 'test', 'portfolio', 'peer_review'
  
  -- Assessment results
  score DECIMAL(4,2),
  max_score DECIMAL(4,2) DEFAULT 100.0,
  verification_level verification_level DEFAULT 'unverified',
  
  -- Assessment data
  assessment_data JSONB DEFAULT '{}'::jsonb,
  evidence_urls JSONB DEFAULT '[]'::jsonb,
  
  -- Verification
  verified_by UUID REFERENCES auth.users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Verification badges table
CREATE TABLE IF NOT EXISTS verification_badges (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  badge_type TEXT NOT NULL, -- 'skill', 'experience', 'education', 'certification'
  badge_name TEXT NOT NULL,
  badge_description TEXT,
  badge_icon TEXT,
  badge_color TEXT DEFAULT '#3B82F6',
  
  -- Verification details
  verification_level verification_level DEFAULT 'unverified',
  verified_by UUID REFERENCES auth.users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  
  -- Badge metadata
  badge_data JSONB DEFAULT '{}'::jsonb,
  display_order INTEGER DEFAULT 0,
  is_visible BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vetting workflow logs table
CREATE TABLE IF NOT EXISTS vetting_workflow_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  application_id UUID NOT NULL REFERENCES vetting_applications(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  
  action TEXT NOT NULL,
  from_status vetting_status,
  to_status vetting_status,
  
  details JSONB DEFAULT '{}'::jsonb,
  notes TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_vetting_applications_user_id ON vetting_applications(user_id);
CREATE INDEX idx_vetting_applications_status ON vetting_applications(status);
CREATE INDEX idx_vetting_applications_type ON vetting_applications(application_type);
CREATE INDEX idx_vetting_reviews_application_id ON vetting_reviews(application_id);
CREATE INDEX idx_vetting_reviews_reviewer_id ON vetting_reviews(reviewer_id);
CREATE INDEX idx_reviewer_assignments_application_id ON reviewer_assignments(application_id);
CREATE INDEX idx_reviewer_assignments_reviewer_id ON reviewer_assignments(reviewer_id);
CREATE INDEX idx_skill_assessments_user_id ON skill_assessments(user_id);
CREATE INDEX idx_verification_badges_user_id ON verification_badges(user_id);
CREATE INDEX idx_vetting_workflow_logs_application_id ON vetting_workflow_logs(application_id);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_vetting_applications_updated_at BEFORE UPDATE ON vetting_applications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vetting_criteria_updated_at BEFORE UPDATE ON vetting_criteria FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vetting_reviews_updated_at BEFORE UPDATE ON vetting_reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reviewer_assignments_updated_at BEFORE UPDATE ON reviewer_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_skill_assessments_updated_at BEFORE UPDATE ON skill_assessments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_verification_badges_updated_at BEFORE UPDATE ON verification_badges FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE vetting_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE vetting_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviewer_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE skill_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE verification_badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE vetting_workflow_logs ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be expanded based on specific requirements)
CREATE POLICY "Users can view their own applications" ON vetting_applications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own applications" ON vetting_applications FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own pending applications" ON vetting_applications FOR UPDATE USING (auth.uid() = user_id AND status = 'pending');

CREATE POLICY "Reviewers can view assigned applications" ON vetting_applications FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM reviewer_assignments 
    WHERE application_id = vetting_applications.id 
    AND reviewer_id = auth.uid()
  )
);

CREATE POLICY "Users can view their own badges" ON verification_badges FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Public can view visible badges" ON verification_badges FOR SELECT USING (is_visible = true);

-- Insert default vetting criteria
INSERT INTO vetting_criteria (application_type, criterion_name, criterion_description, weight, min_score, max_score, is_required) VALUES
('developer', 'Technical Skills', 'Programming languages and technical expertise', 3.00, 0.00, 9.99, true),
('developer', 'Portfolio Quality', 'Quality and relevance of previous work', 2.50, 0.00, 9.99, true),
('developer', 'Communication', 'Written and verbal communication skills', 2.00, 0.00, 9.99, true),
('developer', 'Experience Level', 'Years of relevant experience', 1.50, 0.00, 9.99, false),
('developer', 'Code Quality', 'Clean code practices and standards', 2.00, 0.00, 9.99, true),

('designer', 'Design Portfolio', 'Quality and creativity of design work', 3.00, 0.00, 9.99, true),
('designer', 'Design Tools', 'Proficiency with design software', 2.00, 0.00, 9.99, true),
('designer', 'User Experience', 'UX/UI design understanding', 2.50, 0.00, 9.99, true),
('designer', 'Communication', 'Ability to present and explain designs', 2.00, 0.00, 9.99, true),
('designer', 'Brand Understanding', 'Brand consistency and guidelines', 1.50, 0.00, 9.99, false);

COMMENT ON TABLE vetting_applications IS 'Stores user applications for platform vetting and verification';
COMMENT ON TABLE vetting_criteria IS 'Defines the criteria used to evaluate vetting applications';
COMMENT ON TABLE vetting_reviews IS 'Stores reviewer evaluations of vetting applications';
COMMENT ON TABLE reviewer_assignments IS 'Manages assignment of reviewers to applications';
COMMENT ON TABLE skill_assessments IS 'Tracks skill assessments and verifications for users';
COMMENT ON TABLE verification_badges IS 'Stores verification badges earned by users';
COMMENT ON TABLE vetting_workflow_logs IS 'Audit log for vetting workflow state changes';
