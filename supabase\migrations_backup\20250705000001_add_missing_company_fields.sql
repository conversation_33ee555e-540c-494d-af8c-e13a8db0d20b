-- Add missing company fields to projects table for agreement generation
-- These fields are required for legal agreement generation

DO $$
BEGIN
    -- Add company_city column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'company_city') THEN
        ALTER TABLE public.projects ADD COLUMN company_city TEXT;
        RAISE NOTICE 'Added company_city column to projects table';
    ELSE
        RAISE NOTICE 'company_city column already exists in projects table';
    END IF;

    -- Add contact_email column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'contact_email') THEN
        ALTER TABLE public.projects ADD COLUMN contact_email TEXT;
        RAISE NOTICE 'Added contact_email column to projects table';
    ELSE
        RAISE NOTICE 'contact_email column already exists in projects table';
    END IF;

    -- Add signer_name column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'signer_name') THEN
        ALTER TABLE public.projects ADD COLUMN signer_name TEXT;
        RAISE NOTICE 'Added signer_name column to projects table';
    ELSE
        RAISE NOTICE 'signer_name column already exists in projects table';
    END IF;

    -- Add signer_title column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'signer_title') THEN
        ALTER TABLE public.projects ADD COLUMN signer_title TEXT;
        RAISE NOTICE 'Added signer_title column to projects table';
    ELSE
        RAISE NOTICE 'signer_title column already exists in projects table';
    END IF;
END $$;

-- Add comments to document the purpose of these fields
COMMENT ON COLUMN public.projects.company_city IS 'City where the company is located - required for legal agreements';
COMMENT ON COLUMN public.projects.contact_email IS 'Primary contact email for the company - required for legal agreements';
COMMENT ON COLUMN public.projects.signer_name IS 'Full name of person authorized to sign agreements on behalf of the company';
COMMENT ON COLUMN public.projects.signer_title IS 'Official title of the authorized signer (e.g. CEO, President)';
