-- CRITICAL COMPLIANCE: Company Entity System
-- Day 1 - Developer 1: Legal entity management for business compliance

-- Create companies table for legal entity management
CREATE TABLE public.companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Legal Information (REQUIRED for compliance)
    legal_name TEXT NOT NULL,
    tax_id TEXT NOT NULL UNIQUE, -- EIN/Tax ID
    company_type TEXT NOT NULL CHECK (company_type IN ('corporation', 'llc', 'partnership', 'sole_proprietorship')),
    incorporation_state TEXT,
    incorporation_country TEXT DEFAULT 'US',
    incorporation_date DATE,
    
    -- Business Information
    doing_business_as TEXT, -- DBA name
    industry_classification TEXT, -- NAICS code
    business_description TEXT,
    website_url TEXT,
    
    -- Contact Information (REQUIRED)
    primary_address JSONB NOT NULL, -- Full address object
    mailing_address JSONB, -- If different from primary
    primary_email TEXT NOT NULL,
    primary_phone TEXT,
    
    -- Financial Information
    fiscal_year_end DATE DEFAULT (CURRENT_DATE + INTERVAL '1 year'),
    accounting_method TEXT DEFAULT 'accrual' CHECK (accounting_method IN ('accrual', 'cash')),
    base_currency TEXT DEFAULT 'USD',
    
    -- Status and Compliance
    is_active BOOLEAN DEFAULT true,
    dissolution_date DATE,
    compliance_status TEXT DEFAULT 'active' CHECK (compliance_status IN ('active', 'suspended', 'dissolved')),
    
    -- Audit Trail
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS (Row Level Security)
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;

-- Create indexes for performance
CREATE INDEX idx_companies_tax_id ON public.companies(tax_id);
CREATE INDEX idx_companies_active ON public.companies(is_active);
CREATE INDEX idx_companies_type ON public.companies(company_type);
CREATE INDEX idx_companies_created_by ON public.companies(created_by);

-- Link alliances to legal entities
ALTER TABLE public.teams ADD COLUMN company_id UUID REFERENCES public.companies(id) ON DELETE SET NULL;
ALTER TABLE public.teams ADD COLUMN is_business_entity BOOLEAN DEFAULT false;
ALTER TABLE public.teams ADD COLUMN legal_entity_type TEXT DEFAULT 'informal' CHECK (legal_entity_type IN ('informal', 'formal', 'subsidiary'));

-- Create indexes for team-company relationships
CREATE INDEX idx_teams_company_id ON public.teams(company_id);
CREATE INDEX idx_teams_business_entity ON public.teams(is_business_entity);

-- RLS Policies for companies
CREATE POLICY "Users can view companies they have access to" ON public.companies
    FOR SELECT USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM public.teams t 
            JOIN public.team_members tm ON t.id = tm.team_id 
            WHERE t.company_id = companies.id AND tm.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create companies" ON public.companies
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Company creators and admins can update companies" ON public.companies
    FOR UPDATE USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM public.teams t 
            JOIN public.team_members tm ON t.id = tm.team_id 
            WHERE t.company_id = companies.id 
            AND tm.user_id = auth.uid() 
            AND tm.is_admin = true
        )
    );

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_companies_updated_at 
    BEFORE UPDATE ON public.companies 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to validate company data
CREATE OR REPLACE FUNCTION validate_company_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate tax_id format (basic US EIN validation)
    IF NEW.incorporation_country = 'US' AND NEW.tax_id !~ '^\d{2}-\d{7}$' THEN
        RAISE EXCEPTION 'Invalid US EIN format. Must be XX-XXXXXXX';
    END IF;
    
    -- Validate primary_address has required fields
    IF NEW.primary_address IS NULL OR 
       NOT (NEW.primary_address ? 'street' AND 
            NEW.primary_address ? 'city' AND 
            NEW.primary_address ? 'state' AND 
            NEW.primary_address ? 'zip') THEN
        RAISE EXCEPTION 'Primary address must include street, city, state, and zip';
    END IF;
    
    -- Validate email format
    IF NEW.primary_email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
        RAISE EXCEPTION 'Invalid email format';
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for company data validation
CREATE TRIGGER validate_company_data_trigger
    BEFORE INSERT OR UPDATE ON public.companies
    FOR EACH ROW EXECUTE FUNCTION validate_company_data();

-- Create view for company summary information
CREATE VIEW public.company_summary AS
SELECT 
    c.id,
    c.legal_name,
    c.doing_business_as,
    c.company_type,
    c.incorporation_state,
    c.is_active,
    c.created_at,
    COUNT(t.id) as alliance_count,
    COUNT(CASE WHEN t.is_business_entity THEN 1 END) as business_alliance_count
FROM public.companies c
LEFT JOIN public.teams t ON c.id = t.company_id
GROUP BY c.id, c.legal_name, c.doing_business_as, c.company_type, c.incorporation_state, c.is_active, c.created_at;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.companies TO authenticated;
GRANT SELECT ON public.company_summary TO authenticated;
GRANT USAGE ON SEQUENCE companies_id_seq TO authenticated;

-- Insert sample data for testing (VRC example)
INSERT INTO public.companies (
    legal_name,
    tax_id,
    company_type,
    incorporation_state,
    incorporation_country,
    doing_business_as,
    industry_classification,
    business_description,
    website_url,
    primary_address,
    primary_email,
    primary_phone,
    fiscal_year_end,
    accounting_method,
    created_by
) VALUES (
    'VRC Entertainment LLC',
    '12-3456789',
    'llc',
    'CA',
    'US',
    'VRC Films',
    '512110', -- Motion Picture and Video Production
    'Independent film production and talent management company',
    'https://vrcfilms.com',
    '{"street": "123 Hollywood Blvd", "city": "Los Angeles", "state": "CA", "zip": "90028", "country": "US"}',
    '<EMAIL>',
    '******-123-4567',
    '2024-12-31',
    'accrual',
    (SELECT id FROM auth.users LIMIT 1) -- Use first available user for testing
) ON CONFLICT (tax_id) DO NOTHING;

-- Add comment for documentation
COMMENT ON TABLE public.companies IS 'Legal entity management for business compliance. Required for tax reporting, financial transactions, and regulatory compliance.';
COMMENT ON COLUMN public.companies.tax_id IS 'Federal Tax ID (EIN) - required for all business transactions and tax reporting';
COMMENT ON COLUMN public.companies.primary_address IS 'JSON object with street, city, state, zip, country fields';
COMMENT ON COLUMN public.companies.fiscal_year_end IS 'End date of fiscal year for tax reporting purposes';
