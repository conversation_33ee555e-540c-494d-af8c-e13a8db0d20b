-- Create project_activities table for tracking project activity

-- Check if the project_activities table exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'project_activities'
    ) THEN
        -- Create the project_activities table
        CREATE TABLE public.project_activities (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
            user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
            activity_type TEXT NOT NULL,
            activity_data JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            is_public BOOLEAN DEFAULT true
        );

        -- Create indexes
        CREATE INDEX idx_project_activities_project_id ON public.project_activities(project_id);
        CREATE INDEX idx_project_activities_user_id ON public.project_activities(user_id);
        CREATE INDEX idx_project_activities_created_at ON public.project_activities(created_at);
        
        -- Set up RLS policies
        ALTER TABLE public.project_activities ENABLE ROW LEVEL SECURITY;
        
        -- Anyone can view public activities for projects they are a member of
        CREATE POLICY "View public project activities" 
        ON public.project_activities FOR SELECT 
        USING (
            is_public = true AND
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_contributors.project_id = project_activities.project_id
                AND project_contributors.user_id = auth.uid()
                AND project_contributors.status = 'active'
            )
        );
        
        -- Project admins can view all activities
        CREATE POLICY "Admins can view all project activities" 
        ON public.project_activities FOR SELECT 
        USING (
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_contributors.project_id = project_activities.project_id
                AND project_contributors.user_id = auth.uid()
                AND project_contributors.status = 'active'
                AND (
                    project_contributors.permission_level = 'Owner' OR 
                    project_contributors.permission_level = 'Admin'
                )
            )
        );
        
        -- Only authenticated users can insert activities
        CREATE POLICY "Users can insert their own activities" 
        ON public.project_activities FOR INSERT 
        WITH CHECK (
            auth.uid() = user_id AND
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_contributors.project_id = project_activities.project_id
                AND project_contributors.user_id = auth.uid()
                AND project_contributors.status = 'active'
            )
        );
        
        -- Only admins can update or delete activities
        CREATE POLICY "Admins can update project activities" 
        ON public.project_activities FOR UPDATE 
        USING (
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_contributors.project_id = project_activities.project_id
                AND project_contributors.user_id = auth.uid()
                AND project_contributors.status = 'active'
                AND (
                    project_contributors.permission_level = 'Owner' OR 
                    project_contributors.permission_level = 'Admin'
                )
            )
        );
        
        CREATE POLICY "Admins can delete project activities" 
        ON public.project_activities FOR DELETE 
        USING (
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_contributors.project_id = project_activities.project_id
                AND project_contributors.user_id = auth.uid()
                AND project_contributors.status = 'active'
                AND (
                    project_contributors.permission_level = 'Owner' OR 
                    project_contributors.permission_level = 'Admin'
                )
            )
        );
        
        RAISE NOTICE 'Created project_activities table';
    ELSE
        RAISE NOTICE 'project_activities table already exists';
    END IF;
END $$;
