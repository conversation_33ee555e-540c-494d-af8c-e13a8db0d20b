-- Enhanced Agreement System Migration
-- This migration creates comprehensive database schema for agreement generation
-- supporting all user types, revenue models, and IP rights management

-- ============================================================================
-- STEP 1: ENHANCE ALLIANCE (TEAMS) TABLE
-- ============================================================================

-- First ensure the business_model column exists (from previous migrations)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'business_model') THEN
        ALTER TABLE public.teams ADD COLUMN business_model JSONB DEFAULT '{}'::jsonb;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'industry') THEN
        ALTER TABLE public.teams ADD COLUMN industry VARCHAR(100);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'alliance_type') THEN
        ALTER TABLE public.teams ADD COLUMN alliance_type TEXT DEFAULT 'emerging' CHECK (alliance_type IN ('emerging', 'established', 'solo'));
    END IF;
END $$;

-- Add comprehensive business details to teams table
ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS legal_entity_info JSONB DEFAULT '{}'::jsonb;
ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS business_address JSONB DEFAULT '{}'::jsonb;
ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS contact_information JSONB DEFAULT '{}'::jsonb;
ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS incorporation_details JSONB DEFAULT '{}'::jsonb;
ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS default_revenue_model JSONB DEFAULT '{}'::jsonb;

-- Add indexes for performance (only if columns exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'legal_entity_info') THEN
        CREATE INDEX IF NOT EXISTS idx_teams_legal_entity ON public.teams USING GIN (legal_entity_info);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'business_model') THEN
        CREATE INDEX IF NOT EXISTS idx_teams_business_model ON public.teams USING GIN (business_model);
    END IF;
END $$;

-- ============================================================================
-- STEP 2: ENHANCE VENTURES (PROJECTS) TABLE
-- ============================================================================

-- First ensure the venture columns exist (from previous migrations)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'venture_type') THEN
        ALTER TABLE public.projects ADD COLUMN venture_type TEXT DEFAULT 'software' CHECK (venture_type IN ('software', 'game', 'film', 'music', 'art', 'business', 'research', 'other'));
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'revenue_model') THEN
        ALTER TABLE public.projects ADD COLUMN revenue_model JSONB DEFAULT '{}'::jsonb;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'alliance_id') THEN
        ALTER TABLE public.projects ADD COLUMN alliance_id UUID REFERENCES public.teams(id);
    END IF;
END $$;

-- Add comprehensive venture details
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS tech_stack JSONB DEFAULT '{}'::jsonb;
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS specifications JSONB DEFAULT '{}'::jsonb;
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS roadmap JSONB DEFAULT '{}'::jsonb;
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS ip_ownership_model JSONB DEFAULT '{}'::jsonb;
ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS performance_metrics JSONB DEFAULT '{}'::jsonb;

-- Add indexes for performance (only if columns exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'venture_type') THEN
        CREATE INDEX IF NOT EXISTS idx_projects_venture_type ON public.projects (venture_type);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'tech_stack') THEN
        CREATE INDEX IF NOT EXISTS idx_projects_tech_stack ON public.projects USING GIN (tech_stack);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'revenue_model') THEN
        CREATE INDEX IF NOT EXISTS idx_projects_revenue_model ON public.projects USING GIN (revenue_model);
    END IF;
END $$;

-- ============================================================================
-- STEP 3: CREATE AGREEMENT TEMPLATES TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.agreement_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Template identification
    name VARCHAR(255) NOT NULL,
    template_type VARCHAR(50) NOT NULL, -- 'standard', 'simplified', 'detailed'
    industry VARCHAR(100) NOT NULL, -- 'technology', 'creative', 'service', 'commission'
    collaboration_type VARCHAR(100) NOT NULL, -- 'development', 'music', 'film', 'consulting', 'sales'
    
    -- Template content
    template_content TEXT NOT NULL,
    template_variables JSONB DEFAULT '{}'::jsonb, -- Variables that can be replaced
    
    -- Template metadata
    description TEXT,
    version VARCHAR(20) DEFAULT '1.0',
    is_active BOOLEAN DEFAULT true,
    legal_review_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
    legal_reviewer_id UUID REFERENCES auth.users(id),
    legal_review_date TIMESTAMP WITH TIME ZONE,
    legal_review_notes TEXT,
    
    -- Audit trail
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_agreement_templates_type ON public.agreement_templates (template_type);
CREATE INDEX IF NOT EXISTS idx_agreement_templates_industry ON public.agreement_templates (industry);
CREATE INDEX IF NOT EXISTS idx_agreement_templates_collaboration ON public.agreement_templates (collaboration_type);
CREATE INDEX IF NOT EXISTS idx_agreement_templates_active ON public.agreement_templates (is_active);

-- ============================================================================
-- STEP 4: CREATE REVENUE MODELS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.revenue_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Model identification
    name VARCHAR(255) NOT NULL,
    model_type VARCHAR(50) NOT NULL, -- 'percentage', 'tiered', 'waterfall', 'commission', 'equity'
    industry VARCHAR(100) NOT NULL,
    
    -- Model configuration
    configuration JSONB NOT NULL DEFAULT '{}'::jsonb,
    calculation_rules JSONB DEFAULT '{}'::jsonb,
    payment_terms JSONB DEFAULT '{}'::jsonb,
    
    -- Model metadata
    description TEXT,
    is_template BOOLEAN DEFAULT false, -- True for reusable templates
    is_active BOOLEAN DEFAULT true,
    
    -- Ownership
    created_by UUID NOT NULL REFERENCES auth.users(id),
    alliance_id UUID REFERENCES public.teams(id), -- If specific to an alliance
    
    -- Audit trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_revenue_models_type ON public.revenue_models (model_type);
CREATE INDEX IF NOT EXISTS idx_revenue_models_industry ON public.revenue_models (industry);
CREATE INDEX IF NOT EXISTS idx_revenue_models_alliance ON public.revenue_models (alliance_id);
CREATE INDEX IF NOT EXISTS idx_revenue_models_template ON public.revenue_models (is_template);

-- ============================================================================
-- STEP 5: CREATE IP RIGHTS FRAMEWORK TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.ip_rights_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Rights identification
    name VARCHAR(255) NOT NULL,
    ownership_type VARCHAR(50) NOT NULL, -- 'work_for_hire', 'retained', 'co_ownership', 'licensing'
    industry VARCHAR(100) NOT NULL,
    
    -- Rights configuration
    ownership_details JSONB NOT NULL DEFAULT '{}'::jsonb,
    licensing_terms JSONB DEFAULT '{}'::jsonb,
    attribution_requirements JSONB DEFAULT '{}'::jsonb,
    derivative_rights JSONB DEFAULT '{}'::jsonb,
    
    -- Rights metadata
    description TEXT,
    is_template BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    
    -- Ownership
    created_by UUID NOT NULL REFERENCES auth.users(id),
    alliance_id UUID REFERENCES public.teams(id),
    
    -- Audit trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_ip_rights_ownership_type ON public.ip_rights_models (ownership_type);
CREATE INDEX IF NOT EXISTS idx_ip_rights_industry ON public.ip_rights_models (industry);
CREATE INDEX IF NOT EXISTS idx_ip_rights_alliance ON public.ip_rights_models (alliance_id);

-- ============================================================================
-- STEP 6: CREATE GENERATED AGREEMENTS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.generated_agreements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Agreement identification
    agreement_name VARCHAR(255) NOT NULL,
    agreement_type VARCHAR(100) NOT NULL,
    
    -- Related entities
    alliance_id UUID REFERENCES public.teams(id),
    venture_id UUID REFERENCES public.projects(id),
    template_id UUID REFERENCES public.agreement_templates(id),
    revenue_model_id UUID REFERENCES public.revenue_models(id),
    ip_rights_model_id UUID REFERENCES public.ip_rights_models(id),
    
    -- Agreement content
    agreement_content TEXT NOT NULL,
    generation_parameters JSONB DEFAULT '{}'::jsonb,
    
    -- Agreement status
    status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'review', 'approved', 'signed', 'active', 'terminated'
    version VARCHAR(20) DEFAULT '1.0',
    
    -- Parties
    company_signatory JSONB DEFAULT '{}'::jsonb,
    contributor_signatory JSONB DEFAULT '{}'::jsonb,
    
    -- Signatures and dates
    company_signed_at TIMESTAMP WITH TIME ZONE,
    contributor_signed_at TIMESTAMP WITH TIME ZONE,
    effective_date DATE,
    expiration_date DATE,
    
    -- File storage
    document_url TEXT,
    signed_document_url TEXT,
    
    -- Audit trail
    generated_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_generated_agreements_alliance ON public.generated_agreements (alliance_id);
CREATE INDEX IF NOT EXISTS idx_generated_agreements_venture ON public.generated_agreements (venture_id);
CREATE INDEX IF NOT EXISTS idx_generated_agreements_status ON public.generated_agreements (status);
CREATE INDEX IF NOT EXISTS idx_generated_agreements_type ON public.generated_agreements (agreement_type);

-- ============================================================================
-- STEP 7: CREATE AGREEMENT AUDIT LOG TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.agreement_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Related agreement
    agreement_id UUID NOT NULL REFERENCES public.generated_agreements(id) ON DELETE CASCADE,
    
    -- Action details
    action VARCHAR(100) NOT NULL, -- 'created', 'modified', 'reviewed', 'signed', 'terminated'
    action_details JSONB DEFAULT '{}'::jsonb,
    previous_values JSONB DEFAULT '{}'::jsonb,
    new_values JSONB DEFAULT '{}'::jsonb,
    
    -- Actor
    performed_by UUID NOT NULL REFERENCES auth.users(id),
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamp
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_agreement_audit_agreement ON public.agreement_audit_log (agreement_id);
CREATE INDEX IF NOT EXISTS idx_agreement_audit_action ON public.agreement_audit_log (action);
CREATE INDEX IF NOT EXISTS idx_agreement_audit_date ON public.agreement_audit_log (performed_at);

-- ============================================================================
-- STEP 8: ADD COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE public.agreement_templates IS 'Stores reusable agreement templates for different industries and collaboration types';
COMMENT ON TABLE public.revenue_models IS 'Defines revenue sharing models and calculation rules';
COMMENT ON TABLE public.ip_rights_models IS 'Manages intellectual property rights and licensing frameworks';
COMMENT ON TABLE public.generated_agreements IS 'Stores generated agreements with full content and metadata';
COMMENT ON TABLE public.agreement_audit_log IS 'Tracks all changes and actions performed on agreements';

-- ============================================================================
-- STEP 9: ENABLE ROW LEVEL SECURITY
-- ============================================================================

-- Enable RLS on all new tables
ALTER TABLE public.agreement_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.revenue_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ip_rights_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.generated_agreements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agreement_audit_log ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 10: CREATE RLS POLICIES
-- ============================================================================

-- Agreement Templates Policies
CREATE POLICY "Agreement templates are viewable by authenticated users" ON public.agreement_templates
    FOR SELECT
    USING (auth.uid() IS NOT NULL AND is_active = true);

CREATE POLICY "Agreement templates can be created by authenticated users" ON public.agreement_templates
    FOR INSERT
    WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Agreement templates can be updated by creators" ON public.agreement_templates
    FOR UPDATE
    USING (auth.uid() = created_by);

-- Revenue Models Policies
CREATE POLICY "Revenue models are viewable by creators and alliance members" ON public.revenue_models
    FOR SELECT
    USING (
        auth.uid() = created_by
        OR is_template = true
        OR alliance_id IN (
            SELECT team_id FROM public.team_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Revenue models can be created by authenticated users" ON public.revenue_models
    FOR INSERT
    WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Revenue models can be updated by creators" ON public.revenue_models
    FOR UPDATE
    USING (auth.uid() = created_by);

-- IP Rights Models Policies
CREATE POLICY "IP rights models are viewable by creators and alliance members" ON public.ip_rights_models
    FOR SELECT
    USING (
        auth.uid() = created_by
        OR is_template = true
        OR alliance_id IN (
            SELECT team_id FROM public.team_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "IP rights models can be created by authenticated users" ON public.ip_rights_models
    FOR INSERT
    WITH CHECK (auth.uid() = created_by);

CREATE POLICY "IP rights models can be updated by creators" ON public.ip_rights_models
    FOR UPDATE
    USING (auth.uid() = created_by);

-- Generated Agreements Policies
CREATE POLICY "Generated agreements are viewable by alliance members" ON public.generated_agreements
    FOR SELECT
    USING (
        alliance_id IN (
            SELECT team_id FROM public.team_members
            WHERE user_id = auth.uid()
        )
        OR auth.uid() = generated_by
    );

CREATE POLICY "Generated agreements can be created by alliance members" ON public.generated_agreements
    FOR INSERT
    WITH CHECK (
        auth.uid() = generated_by
        AND alliance_id IN (
            SELECT team_id FROM public.team_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Generated agreements can be updated by alliance admins" ON public.generated_agreements
    FOR UPDATE
    USING (
        alliance_id IN (
            SELECT team_id FROM public.team_members
            WHERE user_id = auth.uid() AND (role = 'admin' OR role = 'owner' OR role = 'founder')
        )
    );

-- Agreement Audit Log Policies
CREATE POLICY "Agreement audit logs are viewable by alliance members" ON public.agreement_audit_log
    FOR SELECT
    USING (
        agreement_id IN (
            SELECT id FROM public.generated_agreements
            WHERE alliance_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Agreement audit logs can be created by authenticated users" ON public.agreement_audit_log
    FOR INSERT
    WITH CHECK (auth.uid() = performed_by);

-- ============================================================================
-- STEP 11: CREATE HELPER FUNCTIONS
-- ============================================================================

-- Function to get available agreement templates for a specific industry
CREATE OR REPLACE FUNCTION get_agreement_templates_for_industry(industry_name TEXT)
RETURNS TABLE (
    id UUID,
    name VARCHAR(255),
    template_type VARCHAR(50),
    collaboration_type VARCHAR(100),
    description TEXT
)
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT
        t.id,
        t.name,
        t.template_type,
        t.collaboration_type,
        t.description
    FROM public.agreement_templates t
    WHERE t.industry = industry_name
    AND t.is_active = true
    AND t.legal_review_status = 'approved'
    ORDER BY t.template_type, t.name;
$$;

-- Function to get revenue models for an alliance
CREATE OR REPLACE FUNCTION get_revenue_models_for_alliance(alliance_uuid UUID)
RETURNS TABLE (
    id UUID,
    name VARCHAR(255),
    model_type VARCHAR(50),
    configuration JSONB,
    description TEXT
)
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT
        r.id,
        r.name,
        r.model_type,
        r.configuration,
        r.description
    FROM public.revenue_models r
    WHERE (r.alliance_id = alliance_uuid OR r.is_template = true)
    AND r.is_active = true
    ORDER BY r.is_template DESC, r.name;
$$;
