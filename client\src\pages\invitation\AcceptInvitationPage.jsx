import React, { useState, useEffect, useContext } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { validateInvitationToken, getInvitationByToken } from '../../utils/invitationUtils';
import { Card, CardBody, CardHeader, Button, Input, Divider } from '@heroui/react';
import { Mail, Users, CheckCircle, AlertCircle, Loader } from 'lucide-react';

const AcceptInvitationPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);
  
  const [invitation, setInvitation] = useState(null);
  const [project, setProject] = useState(null);
  const [inviter, setInviter] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [needsRegistration, setNeedsRegistration] = useState(false);
  
  // Registration form state
  const [registrationData, setRegistrationData] = useState({
    displayName: '',
    password: '',
    confirmPassword: ''
  });

  const invitationId = searchParams.get('id');
  const token = searchParams.get('token');
  const email = searchParams.get('email');

  useEffect(() => {
    if (!invitationId || !token || !email) {
      setError('Invalid invitation link. Please check the link and try again.');
      setLoading(false);
      return;
    }

    loadInvitation();
  }, [invitationId, token, email]);

  const loadInvitation = async () => {
    try {
      setLoading(true);
      
      // Validate token first
      const isValidToken = await validateInvitationToken(token, email, 'project', invitationId);
      if (!isValidToken) {
        setError('This invitation link has expired or is invalid.');
        return;
      }

      // Get invitation details
      const invitationData = await getInvitationByToken('project_contributors', invitationId, token);
      if (!invitationData) {
        setError('Invitation not found or has already been accepted.');
        return;
      }

      setInvitation(invitationData);

      // Get project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('name, description')
        .eq('id', invitationData.project_id)
        .single();

      if (projectError) {
        console.error('Error loading project:', projectError);
      } else {
        setProject(projectData);
      }

      // Get inviter details (if available)
      if (invitationData.user_id) {
        const { data: inviterData, error: inviterError } = await supabase
          .from('users')
          .select('display_name, email')
          .eq('id', invitationData.user_id)
          .single();

        if (!inviterError && inviterData) {
          setInviter(inviterData);
        }
      }

      // Check if user needs to register
      if (!currentUser) {
        setNeedsRegistration(true);
        // Pre-fill display name from email
        setRegistrationData(prev => ({
          ...prev,
          displayName: email.split('@')[0]
        }));
      }

    } catch (error) {
      console.error('Error loading invitation:', error);
      setError('Failed to load invitation details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRegistration = async (e) => {
    e.preventDefault();
    
    if (registrationData.password !== registrationData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (registrationData.password.length < 6) {
      toast.error('Password must be at least 6 characters long');
      return;
    }

    try {
      setProcessing(true);

      // Register user with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: email,
        password: registrationData.password,
        options: {
          data: {
            display_name: registrationData.displayName
          }
        }
      });

      if (authError) {
        throw authError;
      }

      if (authData.user) {
        // User registered successfully, now accept the invitation
        await acceptInvitation(authData.user);
      } else {
        toast.error('Registration failed. Please try again.');
      }

    } catch (error) {
      console.error('Registration error:', error);
      toast.error(error.message || 'Registration failed. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const acceptInvitation = async (user = currentUser) => {
    if (!user || !invitation) return;

    try {
      setProcessing(true);

      // Update invitation status
      const { error: updateError } = await supabase
        .from('project_contributors')
        .update({
          user_id: user.id,
          status: 'active',
          joined_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', invitation.id);

      if (updateError) {
        throw updateError;
      }

      toast.success('Invitation accepted successfully!');
      
      // Redirect to project page
      navigate(`/project/${invitation.project_id}`);

    } catch (error) {
      console.error('Error accepting invitation:', error);
      toast.error('Failed to accept invitation. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="flex flex-col items-center justify-center p-8">
            <Loader className="w-8 h-8 animate-spin text-white mb-4" />
            <p className="text-white">Loading invitation...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="flex flex-col items-center justify-center p-8">
            <AlertCircle className="w-12 h-12 text-red-400 mb-4" />
            <h2 className="text-xl font-bold text-white mb-2">Invalid Invitation</h2>
            <p className="text-white/70 text-center mb-6">{error}</p>
            <Button 
              color="primary" 
              onClick={() => navigate('/')}
            >
              Go to Home
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-lg bg-white/10 backdrop-blur-md border-white/20">
        <CardHeader className="pb-0">
          <div className="flex flex-col items-center w-full">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
              <Mail className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-white text-center">
              Project Invitation
            </h1>
          </div>
        </CardHeader>
        
        <CardBody className="pt-4">
          {/* Invitation Details */}
          <div className="bg-white/5 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-white mb-2">
              {project?.name || 'Project Invitation'}
            </h3>
            <p className="text-white/70 mb-3">
              {project?.description || 'You have been invited to join this project.'}
            </p>
            <div className="flex items-center gap-2 text-sm text-white/60">
              <Users className="w-4 h-4" />
              <span>Role: {invitation?.role || 'Contributor'}</span>
            </div>
            {inviter && (
              <div className="flex items-center gap-2 text-sm text-white/60 mt-1">
                <Mail className="w-4 h-4" />
                <span>Invited by: {inviter.display_name || inviter.email}</span>
              </div>
            )}
          </div>

          {needsRegistration ? (
            /* Registration Form */
            <form onSubmit={handleRegistration} className="space-y-4">
              <div className="text-center mb-4">
                <h3 className="text-lg font-semibold text-white mb-2">
                  Create Your Account
                </h3>
                <p className="text-white/70 text-sm">
                  You need to create an account to accept this invitation
                </p>
              </div>

              <Input
                label="Display Name"
                placeholder="Enter your display name"
                value={registrationData.displayName}
                onChange={(e) => setRegistrationData(prev => ({
                  ...prev,
                  displayName: e.target.value
                }))}
                required
                className="text-white"
              />

              <Input
                label="Email"
                value={email}
                disabled
                className="text-white"
              />

              <Input
                label="Password"
                type="password"
                placeholder="Enter your password"
                value={registrationData.password}
                onChange={(e) => setRegistrationData(prev => ({
                  ...prev,
                  password: e.target.value
                }))}
                required
                className="text-white"
              />

              <Input
                label="Confirm Password"
                type="password"
                placeholder="Confirm your password"
                value={registrationData.confirmPassword}
                onChange={(e) => setRegistrationData(prev => ({
                  ...prev,
                  confirmPassword: e.target.value
                }))}
                required
                className="text-white"
              />

              <Button
                type="submit"
                color="primary"
                size="lg"
                className="w-full"
                isLoading={processing}
                startContent={!processing && <CheckCircle className="w-5 h-5" />}
              >
                {processing ? 'Creating Account...' : 'Create Account & Accept Invitation'}
              </Button>
            </form>
          ) : (
            /* Accept Invitation */
            <div className="text-center">
              <p className="text-white/70 mb-6">
                Welcome back! Click below to accept this invitation and join the project.
              </p>
              
              <Button
                color="primary"
                size="lg"
                className="w-full"
                onClick={() => acceptInvitation()}
                isLoading={processing}
                startContent={!processing && <CheckCircle className="w-5 h-5" />}
              >
                {processing ? 'Accepting...' : 'Accept Invitation'}
              </Button>
            </div>
          )}

          <Divider className="my-6" />
          
          <div className="text-center">
            <Button
              variant="light"
              color="default"
              onClick={() => navigate('/')}
              className="text-white/70"
            >
              Cancel
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default AcceptInvitationPage;
