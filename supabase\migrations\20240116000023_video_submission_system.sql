-- Video Submission and Recommendation System
-- Allows community to submit and recommend educational videos

-- Video submission status enum
CREATE TYPE video_submission_status AS ENUM (
  'pending',
  'under_review',
  'approved',
  'rejected',
  'needs_revision'
);

-- Video quality rating enum
CREATE TYPE video_quality_rating AS ENUM (
  'excellent',
  'good',
  'fair',
  'poor'
);

-- Video submissions table
CREATE TABLE video_submissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Submitter information
  submitted_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  submitter_name TEXT,
  submitter_email TEXT,
  
  -- Video information
  video_url TEXT NOT NULL,
  video_id TEXT NOT NULL, -- Extracted YouTube video ID
  title TEXT NOT NULL,
  description TEXT,
  channel_name TEXT,
  duration_minutes INTEGER,
  
  -- Educational metadata
  skills JSONB DEFAULT '[]'::jsonb,
  categories JSONB DEFAULT '[]'::jsonb,
  difficulty_level TEXT, -- beginner, intermediate, advanced
  learning_objectives TEXT[],
  prerequisites TEXT[],
  
  -- Submission details
  submission_reason TEXT, -- Why this video should be included
  target_audience TEXT, -- Who would benefit from this video
  quality_notes TEXT, -- Notes about video quality/content
  
  -- Review information
  status video_submission_status DEFAULT 'pending',
  reviewed_by UUID REFERENCES auth.users(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  review_notes TEXT,
  quality_rating video_quality_rating,
  
  -- Approval tracking
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_youtube_url CHECK (video_url ~* '^https?://(www\.)?(youtube\.com/watch\?v=|youtu\.be/)')
);

-- Video recommendations table
CREATE TABLE video_recommendations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Recommendation details
  video_id TEXT NOT NULL, -- YouTube video ID
  recommended_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  recommended_to UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- NULL for public recommendations
  
  -- Recommendation context
  skill_context TEXT, -- Which skill this video helps with
  learning_path_context UUID REFERENCES learning_paths(id),
  recommendation_reason TEXT NOT NULL,
  
  -- Video metadata (cached for performance)
  video_title TEXT,
  video_description TEXT,
  video_thumbnail TEXT,
  video_duration INTEGER,
  
  -- Engagement tracking
  upvotes INTEGER DEFAULT 0,
  downvotes INTEGER DEFAULT 0,
  views INTEGER DEFAULT 0,
  
  -- Status
  is_public BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Video recommendation votes table
CREATE TABLE video_recommendation_votes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  recommendation_id UUID NOT NULL REFERENCES video_recommendations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  vote_type TEXT NOT NULL CHECK (vote_type IN ('upvote', 'downvote')),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one vote per user per recommendation
  UNIQUE(recommendation_id, user_id)
);

-- Video submission reviews table (for detailed review process)
CREATE TABLE video_submission_reviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  submission_id UUID NOT NULL REFERENCES video_submissions(id) ON DELETE CASCADE,
  reviewer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Review criteria scores (1-5 scale)
  content_quality_score INTEGER CHECK (content_quality_score >= 1 AND content_quality_score <= 5),
  educational_value_score INTEGER CHECK (educational_value_score >= 1 AND educational_value_score <= 5),
  production_quality_score INTEGER CHECK (production_quality_score >= 1 AND production_quality_score <= 5),
  accuracy_score INTEGER CHECK (accuracy_score >= 1 AND accuracy_score <= 5),
  
  -- Review details
  review_comments TEXT,
  recommended_action TEXT CHECK (recommended_action IN ('approve', 'reject', 'needs_revision')),
  revision_suggestions TEXT,
  
  -- Overall assessment
  overall_rating video_quality_rating,
  would_recommend BOOLEAN,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Video learning analytics table
CREATE TABLE video_learning_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  video_id TEXT NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Viewing analytics
  total_watch_time_minutes DECIMAL(8,2) DEFAULT 0,
  completion_percentage DECIMAL(5,2) DEFAULT 0,
  times_watched INTEGER DEFAULT 0,
  
  -- Learning outcomes
  skill_improvement_rating INTEGER CHECK (skill_improvement_rating >= 1 AND skill_improvement_rating <= 5),
  difficulty_rating INTEGER CHECK (difficulty_rating >= 1 AND difficulty_rating <= 5),
  would_recommend BOOLEAN,
  
  -- Feedback
  user_notes TEXT,
  user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
  
  -- Timestamps
  first_watched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_watched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  
  UNIQUE(video_id, user_id)
);

-- Indexes for performance
CREATE INDEX idx_video_submissions_status ON video_submissions(status);
CREATE INDEX idx_video_submissions_submitted_by ON video_submissions(submitted_by);
CREATE INDEX idx_video_submissions_skills ON video_submissions USING GIN(skills);
CREATE INDEX idx_video_submissions_categories ON video_submissions USING GIN(categories);

CREATE INDEX idx_video_recommendations_recommended_by ON video_recommendations(recommended_by);
CREATE INDEX idx_video_recommendations_skill_context ON video_recommendations(skill_context);
CREATE INDEX idx_video_recommendations_public ON video_recommendations(is_public) WHERE is_public = true;

CREATE INDEX idx_video_recommendation_votes_recommendation ON video_recommendation_votes(recommendation_id);
CREATE INDEX idx_video_recommendation_votes_user ON video_recommendation_votes(user_id);

CREATE INDEX idx_video_submission_reviews_submission ON video_submission_reviews(submission_id);
CREATE INDEX idx_video_submission_reviews_reviewer ON video_submission_reviews(reviewer_id);

CREATE INDEX idx_video_learning_analytics_video ON video_learning_analytics(video_id);
CREATE INDEX idx_video_learning_analytics_user ON video_learning_analytics(user_id);

-- Functions for updating vote counts
CREATE OR REPLACE FUNCTION update_recommendation_vote_counts()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    IF NEW.vote_type = 'upvote' THEN
      UPDATE video_recommendations 
      SET upvotes = upvotes + 1 
      WHERE id = NEW.recommendation_id;
    ELSE
      UPDATE video_recommendations 
      SET downvotes = downvotes + 1 
      WHERE id = NEW.recommendation_id;
    END IF;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    IF OLD.vote_type = 'upvote' THEN
      UPDATE video_recommendations 
      SET upvotes = upvotes - 1 
      WHERE id = OLD.recommendation_id;
    ELSE
      UPDATE video_recommendations 
      SET downvotes = downvotes - 1 
      WHERE id = OLD.recommendation_id;
    END IF;
    RETURN OLD;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Handle vote type change
    IF OLD.vote_type = 'upvote' AND NEW.vote_type = 'downvote' THEN
      UPDATE video_recommendations 
      SET upvotes = upvotes - 1, downvotes = downvotes + 1 
      WHERE id = NEW.recommendation_id;
    ELSIF OLD.vote_type = 'downvote' AND NEW.vote_type = 'upvote' THEN
      UPDATE video_recommendations 
      SET upvotes = upvotes + 1, downvotes = downvotes - 1 
      WHERE id = NEW.recommendation_id;
    END IF;
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Triggers
CREATE TRIGGER trigger_update_recommendation_vote_counts
  AFTER INSERT OR UPDATE OR DELETE ON video_recommendation_votes
  FOR EACH ROW EXECUTE FUNCTION update_recommendation_vote_counts();

-- Update timestamps trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_video_submissions_updated_at
  BEFORE UPDATE ON video_submissions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_video_recommendations_updated_at
  BEFORE UPDATE ON video_recommendations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS)
ALTER TABLE video_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_recommendation_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_submission_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_learning_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Video submissions: Users can view their own submissions, admins can view all
CREATE POLICY "Users can view own submissions" ON video_submissions
  FOR SELECT USING (auth.uid() = submitted_by);

CREATE POLICY "Users can create submissions" ON video_submissions
  FOR INSERT WITH CHECK (auth.uid() = submitted_by);

CREATE POLICY "Users can update own pending submissions" ON video_submissions
  FOR UPDATE USING (auth.uid() = submitted_by AND status = 'pending');

-- Video recommendations: Public recommendations visible to all, private to intended recipient
CREATE POLICY "Public recommendations visible to all" ON video_recommendations
  FOR SELECT USING (is_public = true);

CREATE POLICY "Private recommendations visible to recipient" ON video_recommendations
  FOR SELECT USING (recommended_to = auth.uid());

CREATE POLICY "Users can create recommendations" ON video_recommendations
  FOR INSERT WITH CHECK (auth.uid() = recommended_by);

CREATE POLICY "Users can update own recommendations" ON video_recommendations
  FOR UPDATE USING (auth.uid() = recommended_by);

-- Video recommendation votes: Users can vote and see all votes
CREATE POLICY "Users can view all votes" ON video_recommendation_votes
  FOR SELECT USING (true);

CREATE POLICY "Users can create votes" ON video_recommendation_votes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own votes" ON video_recommendation_votes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own votes" ON video_recommendation_votes
  FOR DELETE USING (auth.uid() = user_id);

-- Video learning analytics: Users can only see their own analytics
CREATE POLICY "Users can view own analytics" ON video_learning_analytics
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own analytics" ON video_learning_analytics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own analytics" ON video_learning_analytics
  FOR UPDATE USING (auth.uid() = user_id);

-- Comments
COMMENT ON TABLE video_submissions IS 'Community-submitted educational videos pending review';
COMMENT ON TABLE video_recommendations IS 'User recommendations for educational videos';
COMMENT ON TABLE video_recommendation_votes IS 'Community voting on video recommendations';
COMMENT ON TABLE video_submission_reviews IS 'Detailed reviews of submitted videos by moderators';
COMMENT ON TABLE video_learning_analytics IS 'User learning analytics and feedback for videos';
