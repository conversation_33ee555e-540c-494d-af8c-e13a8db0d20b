-- Add feature_flags column to user_preferences table if it doesn't exist
DO $$
BEGIN
    -- Check if the user_preferences table exists
    IF EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'user_preferences'
    ) THEN
        -- Check if the feature_flags column already exists
        IF NOT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'user_preferences' 
            AND column_name = 'feature_flags'
        ) THEN
            -- Add the feature_flags column
            ALTER TABLE public.user_preferences 
            ADD COLUMN feature_flags JSONB DEFAULT '{}'::jsonb;
            
            RAISE NOTICE 'Added feature_flags column to user_preferences table';
        ELSE
            RAISE NOTICE 'feature_flags column already exists in user_preferences table';
        END IF;
    ELSE
        -- Create the user_preferences table if it doesn't exist
        CREATE TABLE public.user_preferences (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            theme VARCHAR(50) DEFAULT 'light',
            sound_enabled BOOLEAN DEFAULT true,
            feature_flags JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            UNIQUE(user_id)
        );
        
        -- Add RLS policies
        ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
        
        -- Policy for users to read their own preferences
        CREATE POLICY "Users can read their own preferences" 
        ON public.user_preferences 
        FOR SELECT 
        USING (auth.uid() = user_id);
        
        -- Policy for users to update their own preferences
        CREATE POLICY "Users can update their own preferences" 
        ON public.user_preferences 
        FOR UPDATE 
        USING (auth.uid() = user_id);
        
        -- Policy for users to insert their own preferences
        CREATE POLICY "Users can insert their own preferences" 
        ON public.user_preferences 
        FOR INSERT 
        WITH CHECK (auth.uid() = user_id);
        
        RAISE NOTICE 'Created user_preferences table with feature_flags column';
    END IF;
END
$$;
