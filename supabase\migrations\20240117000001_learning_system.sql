-- Learning Management System Database Schema
-- Migration: 20240116000003_learning_system.sql

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Course provider enum
CREATE TYPE course_provider AS ENUM (
  'linkedin_learning',
  'internal',
  'youtube',
  'udemy',
  'coursera',
  'other'
);

-- Learning status enum
CREATE TYPE learning_status AS ENUM (
  'not_started',
  'in_progress',
  'completed',
  'paused',
  'failed'
);

-- Integration types
CREATE TYPE integration_type AS ENUM (
  'linkedin_learning',
  'github',
  'slack',
  'discord',
  'zoom',
  'other'
);

-- User integrations table
CREATE TABLE user_integrations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  integration_type integration_type NOT NULL,
  
  -- OAuth tokens
  access_token TEXT,
  refresh_token TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  
  -- Integration data
  integration_data JSONB DEFAULT '{}'::jsonb,
  is_active BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, integration_type)
);

-- Learning progress table
CREATE TABLE learning_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Course identification
  course_id TEXT NOT NULL,
  course_provider course_provider NOT NULL,
  course_title TEXT,
  course_url TEXT,
  
  -- Progress tracking
  status learning_status DEFAULT 'not_started',
  completion_percentage DECIMAL(5,2) DEFAULT 0.0,
  time_spent_minutes INTEGER DEFAULT 0,
  
  -- Timestamps
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Progress data
  progress_data JSONB DEFAULT '{}'::jsonb,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, course_id, course_provider)
);

-- Course catalog table
CREATE TABLE course_catalog (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Course identification
  external_id TEXT NOT NULL,
  provider course_provider NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  
  -- Course details
  duration_minutes INTEGER,
  difficulty_level TEXT, -- beginner, intermediate, advanced
  language TEXT DEFAULT 'en',
  instructor_name TEXT,
  
  -- Media
  thumbnail_url TEXT,
  course_url TEXT,
  
  -- Metadata
  skills JSONB DEFAULT '[]'::jsonb,
  categories JSONB DEFAULT '[]'::jsonb,
  tags JSONB DEFAULT '[]'::jsonb,
  
  -- Ratings and stats
  rating DECIMAL(3,2),
  total_enrollments INTEGER DEFAULT 0,
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  
  -- Timestamps
  published_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(external_id, provider)
);

-- Learning paths table
CREATE TABLE learning_paths (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Path details
  title TEXT NOT NULL,
  description TEXT,
  difficulty_level TEXT,
  estimated_duration_hours INTEGER,
  
  -- Path metadata
  skills JSONB DEFAULT '[]'::jsonb,
  prerequisites JSONB DEFAULT '[]'::jsonb,
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  is_public BOOLEAN DEFAULT true,
  
  -- Creator
  created_by UUID REFERENCES auth.users(id),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning path courses table
CREATE TABLE learning_path_courses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  learning_path_id UUID NOT NULL REFERENCES learning_paths(id) ON DELETE CASCADE,
  course_id UUID NOT NULL REFERENCES course_catalog(id) ON DELETE CASCADE,
  
  -- Order and requirements
  sequence_order INTEGER NOT NULL,
  is_required BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(learning_path_id, course_id),
  UNIQUE(learning_path_id, sequence_order)
);

-- User learning path enrollments
CREATE TABLE learning_path_enrollments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  learning_path_id UUID NOT NULL REFERENCES learning_paths(id) ON DELETE CASCADE,
  
  -- Enrollment status
  status learning_status DEFAULT 'not_started',
  completion_percentage DECIMAL(5,2) DEFAULT 0.0,
  
  -- Timestamps
  enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, learning_path_id)
);

-- Course skill mappings table
CREATE TABLE course_skill_mappings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  course_id TEXT NOT NULL,
  course_provider course_provider NOT NULL,
  
  -- Skill details
  skill_name TEXT NOT NULL,
  skill_category TEXT,
  skill_level TEXT, -- beginner, intermediate, advanced
  
  -- Mapping metadata
  relevance_score DECIMAL(3,2) DEFAULT 1.0, -- 0.0 to 1.0
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(course_id, course_provider, skill_name)
);

-- Learning achievements table
CREATE TABLE learning_achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Achievement details
  achievement_type TEXT NOT NULL, -- course_completion, path_completion, skill_mastery, streak
  achievement_name TEXT NOT NULL,
  achievement_description TEXT,
  
  -- Achievement data
  achievement_data JSONB DEFAULT '{}'::jsonb,
  points_earned INTEGER DEFAULT 0,
  
  -- Badge/icon
  badge_icon TEXT,
  badge_color TEXT DEFAULT '#3B82F6',
  
  -- Timestamps
  earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning recommendations table
CREATE TABLE learning_recommendations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Recommendation details
  recommended_type TEXT NOT NULL, -- course, learning_path, skill
  recommended_id TEXT NOT NULL,
  recommendation_reason TEXT,
  
  -- Recommendation metadata
  relevance_score DECIMAL(3,2) DEFAULT 1.0,
  recommendation_data JSONB DEFAULT '{}'::jsonb,
  
  -- Status
  is_viewed BOOLEAN DEFAULT false,
  is_dismissed BOOLEAN DEFAULT false,
  is_enrolled BOOLEAN DEFAULT false,
  
  -- Timestamps
  viewed_at TIMESTAMP WITH TIME ZONE,
  dismissed_at TIMESTAMP WITH TIME ZONE,
  enrolled_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_user_integrations_user_id ON user_integrations(user_id);
CREATE INDEX idx_user_integrations_type ON user_integrations(integration_type);
CREATE INDEX idx_learning_progress_user_id ON learning_progress(user_id);
CREATE INDEX idx_learning_progress_course ON learning_progress(course_id, course_provider);
CREATE INDEX idx_learning_progress_status ON learning_progress(status);
CREATE INDEX idx_course_catalog_provider ON course_catalog(provider);
CREATE INDEX idx_course_catalog_skills ON course_catalog USING GIN(skills);
CREATE INDEX idx_course_catalog_active ON course_catalog(is_active);
CREATE INDEX idx_learning_path_courses_path_id ON learning_path_courses(learning_path_id);
CREATE INDEX idx_learning_path_enrollments_user_id ON learning_path_enrollments(user_id);
CREATE INDEX idx_course_skill_mappings_course ON course_skill_mappings(course_id, course_provider);
CREATE INDEX idx_course_skill_mappings_skill ON course_skill_mappings(skill_name);
CREATE INDEX idx_learning_achievements_user_id ON learning_achievements(user_id);
CREATE INDEX idx_learning_achievements_type ON learning_achievements(achievement_type);
CREATE INDEX idx_learning_recommendations_user_id ON learning_recommendations(user_id);

-- Create updated_at triggers
CREATE TRIGGER update_user_integrations_updated_at BEFORE UPDATE ON user_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_progress_updated_at BEFORE UPDATE ON learning_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_course_catalog_updated_at BEFORE UPDATE ON course_catalog FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_paths_updated_at BEFORE UPDATE ON learning_paths FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_path_enrollments_updated_at BEFORE UPDATE ON learning_path_enrollments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_recommendations_updated_at BEFORE UPDATE ON learning_recommendations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE user_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_path_enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_recommendations ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can manage their own integrations" ON user_integrations FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view their own learning progress" ON learning_progress FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view their own enrollments" ON learning_path_enrollments FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view their own achievements" ON learning_achievements FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view their own recommendations" ON learning_recommendations FOR ALL USING (auth.uid() = user_id);

-- Public read access for catalog and paths
CREATE POLICY "Public can view active courses" ON course_catalog FOR SELECT USING (is_active = true);
CREATE POLICY "Public can view public learning paths" ON learning_paths FOR SELECT USING (is_public = true);
CREATE POLICY "Public can view learning path courses" ON learning_path_courses FOR SELECT USING (true);
CREATE POLICY "Public can view course skill mappings" ON course_skill_mappings FOR SELECT USING (true);

-- Insert sample learning paths
INSERT INTO learning_paths (title, description, difficulty_level, estimated_duration_hours, skills, is_public, created_by) VALUES
('Full-Stack Development Fundamentals', 'Complete introduction to full-stack web development', 'beginner', 40, '["JavaScript", "React", "Node.js", "Database Design"]', true, null),
('Advanced React Development', 'Master advanced React patterns and performance optimization', 'advanced', 25, '["React", "Redux", "Performance Optimization", "Testing"]', true, null),
('DevOps and Deployment', 'Learn modern DevOps practices and deployment strategies', 'intermediate', 30, '["Docker", "Kubernetes", "CI/CD", "AWS"]', true, null),
('UI/UX Design Mastery', 'Comprehensive design thinking and user experience design', 'intermediate', 35, '["Design Thinking", "Figma", "User Research", "Prototyping"]', true, null);

-- Insert sample course skill mappings for LinkedIn Learning
INSERT INTO course_skill_mappings (course_id, course_provider, skill_name, skill_category, skill_level, relevance_score) VALUES
('react-essential-training', 'linkedin_learning', 'React', 'Frontend Development', 'beginner', 1.0),
('react-essential-training', 'linkedin_learning', 'JavaScript', 'Programming Languages', 'intermediate', 0.8),
('advanced-react', 'linkedin_learning', 'React', 'Frontend Development', 'advanced', 1.0),
('advanced-react', 'linkedin_learning', 'Performance Optimization', 'Frontend Development', 'advanced', 0.9),
('node-js-essential-training', 'linkedin_learning', 'Node.js', 'Backend Development', 'beginner', 1.0),
('node-js-essential-training', 'linkedin_learning', 'JavaScript', 'Programming Languages', 'intermediate', 0.8);

COMMENT ON TABLE user_integrations IS 'Stores user integrations with external services like LinkedIn Learning';
COMMENT ON TABLE learning_progress IS 'Tracks user progress through individual courses';
COMMENT ON TABLE course_catalog IS 'Catalog of available courses from various providers';
COMMENT ON TABLE learning_paths IS 'Structured learning paths combining multiple courses';
COMMENT ON TABLE learning_path_courses IS 'Courses included in learning paths with sequencing';
COMMENT ON TABLE learning_path_enrollments IS 'User enrollments in learning paths';
COMMENT ON TABLE course_skill_mappings IS 'Maps courses to skills they teach';
COMMENT ON TABLE learning_achievements IS 'User achievements and badges from learning activities';
COMMENT ON TABLE learning_recommendations IS 'Personalized learning recommendations for users';
