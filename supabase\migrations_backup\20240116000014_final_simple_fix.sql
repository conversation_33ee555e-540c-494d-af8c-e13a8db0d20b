-- Final Simple Fix - Only create what doesn't exist
-- This migration safely creates only missing components

-- Create collaboration_request_applications table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'collaboration_request_applications') THEN
        CREATE TABLE collaboration_request_applications (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            request_id UUID NOT NULL REFERENCES collaboration_requests(id) ON DELETE CASCADE,
            applicant_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'withdrawn')),
            application_message TEXT,
            proposed_rate DECIMAL(10,2),
            proposed_timeline_days INTEGER,
            portfolio_links TEXT[],
            relevant_experience TEXT,
            applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            reviewed_at TIMESTAMP WITH TIME ZONE,
            reviewed_by UUID REFERENCES auth.users(id),
            review_notes TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(request_id, applicant_id)
        );
        
        -- Enable RLS
        ALTER TABLE collaboration_request_applications ENABLE ROW LEVEL SECURITY;
        
        -- Create indexes
        CREATE INDEX idx_collaboration_request_applications_request ON collaboration_request_applications(request_id);
        CREATE INDEX idx_collaboration_request_applications_applicant ON collaboration_request_applications(applicant_id);
        
        -- Create policies
        CREATE POLICY "Users can view applications for their requests" ON collaboration_request_applications
          FOR SELECT USING (
            request_id IN (SELECT id FROM collaboration_requests WHERE requester_id = auth.uid())
          );

        CREATE POLICY "Users can view their own applications" ON collaboration_request_applications
          FOR SELECT USING (applicant_id = auth.uid());

        CREATE POLICY "Users can create applications" ON collaboration_request_applications
          FOR INSERT WITH CHECK (applicant_id = auth.uid());
    END IF;
END
$$;

-- Create task_gig_relationships table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'task_gig_relationships') THEN
        CREATE TABLE task_gig_relationships (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
          gig_id UUID REFERENCES collaboration_requests(id) ON DELETE CASCADE,
          relationship_type TEXT NOT NULL CHECK (relationship_type IN ('task_to_gig', 'gig_to_task', 'bidirectional')),
          converted_by UUID NOT NULL REFERENCES auth.users(id),
          conversion_reason TEXT,
          original_type TEXT NOT NULL CHECK (original_type IN ('task', 'gig')),
          auto_sync_status BOOLEAN DEFAULT true,
          auto_sync_assignee BOOLEAN DEFAULT true,
          auto_sync_deadline BOOLEAN DEFAULT true,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(task_id, gig_id)
        );
        
        -- Enable RLS
        ALTER TABLE task_gig_relationships ENABLE ROW LEVEL SECURITY;
        
        -- Create indexes
        CREATE INDEX idx_task_gig_relationships_task_id ON task_gig_relationships(task_id);
        CREATE INDEX idx_task_gig_relationships_gig_id ON task_gig_relationships(gig_id);
        
        -- Create policies
        CREATE POLICY "Users can view task-gig relationships for their content" ON task_gig_relationships
          FOR SELECT USING (
            task_id IN (SELECT id FROM tasks WHERE assignee_id = auth.uid() OR created_by = auth.uid()) OR
            gig_id IN (SELECT id FROM collaboration_requests WHERE requester_id = auth.uid())
          );

        CREATE POLICY "Users can create task-gig relationships for their content" ON task_gig_relationships
          FOR INSERT WITH CHECK (
            converted_by = auth.uid() AND (
              task_id IN (SELECT id FROM tasks WHERE assignee_id = auth.uid() OR created_by = auth.uid()) OR
              gig_id IN (SELECT id FROM collaboration_requests WHERE requester_id = auth.uid())
            )
          );
    END IF;
END
$$;

-- Add columns to existing tables if they don't exist
DO $$
BEGIN
    -- Add columns to collaboration_requests
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'collaboration_requests' AND column_name = 'source_task_id') THEN
        ALTER TABLE collaboration_requests ADD COLUMN source_task_id UUID REFERENCES tasks(id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'collaboration_requests' AND column_name = 'auto_assign_on_acceptance') THEN
        ALTER TABLE collaboration_requests ADD COLUMN auto_assign_on_acceptance BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'collaboration_requests' AND column_name = 'task_integration_settings') THEN
        ALTER TABLE collaboration_requests ADD COLUMN task_integration_settings JSONB DEFAULT '{}'::jsonb;
    END IF;
    
    -- Add columns to tasks
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'source_gig_id') THEN
        ALTER TABLE tasks ADD COLUMN source_gig_id UUID REFERENCES collaboration_requests(id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'is_gig_derived') THEN
        ALTER TABLE tasks ADD COLUMN is_gig_derived BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'gig_integration_settings') THEN
        ALTER TABLE tasks ADD COLUMN gig_integration_settings JSONB DEFAULT '{}'::jsonb;
    END IF;
END
$$;

-- Create or replace the conversion functions
CREATE OR REPLACE FUNCTION convert_task_to_gig(
  p_task_id UUID,
  p_user_id UUID,
  p_budget_min DECIMAL DEFAULT NULL,
  p_budget_max DECIMAL DEFAULT NULL,
  p_timeline_days INTEGER DEFAULT NULL,
  p_skill_requirements TEXT[] DEFAULT NULL,
  p_conversion_reason TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_task_record RECORD;
  v_gig_id UUID;
  v_relationship_id UUID;
BEGIN
  -- Get task details
  SELECT * INTO v_task_record
  FROM tasks
  WHERE id = p_task_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Task not found: %', p_task_id;
  END IF;
  
  -- Create collaboration request (gig) using requester_id
  INSERT INTO collaboration_requests (
    title,
    description,
    project_id,
    requester_id,
    request_type,
    budget_min,
    budget_max,
    timeline_days,
    skill_requirements,
    status,
    source_task_id,
    auto_assign_on_acceptance,
    task_integration_settings
  ) VALUES (
    v_task_record.title,
    COALESCE(v_task_record.description, 'Converted from task'),
    v_task_record.project_id,
    p_user_id,
    'task_assistance',
    p_budget_min,
    p_budget_max,
    p_timeline_days,
    p_skill_requirements,
    'open',
    p_task_id,
    true,
    jsonb_build_object(
      'original_task_id', p_task_id,
      'auto_sync_enabled', true,
      'conversion_date', NOW()
    )
  ) RETURNING id INTO v_gig_id;
  
  -- Create relationship record
  INSERT INTO task_gig_relationships (
    task_id,
    gig_id,
    relationship_type,
    converted_by,
    conversion_reason,
    original_type
  ) VALUES (
    p_task_id,
    v_gig_id,
    'task_to_gig',
    p_user_id,
    p_conversion_reason,
    'task'
  ) RETURNING id INTO v_relationship_id;
  
  -- Update task to reference gig
  UPDATE tasks
  SET 
    source_gig_id = v_gig_id,
    is_gig_derived = false,
    gig_integration_settings = jsonb_build_object(
      'converted_to_gig', true,
      'gig_id', v_gig_id,
      'relationship_id', v_relationship_id
    )
  WHERE id = p_task_id;
  
  RETURN v_gig_id;
END;
$$ LANGUAGE plpgsql;

-- Simple function to populate sample data for current user
CREATE OR REPLACE FUNCTION populate_my_sample_data()
RETURNS VOID AS $$
DECLARE
    v_user_id UUID;
    v_project_id UUID;
BEGIN
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to populate sample data';
    END IF;
    
    -- Create a sample project if none exists
    SELECT id INTO v_project_id
    FROM projects 
    WHERE created_by = v_user_id 
    LIMIT 1;
    
    IF v_project_id IS NULL THEN
        INSERT INTO projects (id, name, description, status, created_by, created_at)
        VALUES (
            uuid_generate_v4(),
            'Sample Project',
            'A demonstration project to showcase Royaltea features',
            'active',
            v_user_id,
            NOW()
        )
        RETURNING id INTO v_project_id;
    END IF;

    -- Insert sample user activity logs (only if table exists)
    BEGIN
        INSERT INTO user_activity_logs (user_id, session_id, event_type, event_category, event_action, from_page, to_page, navigation_method, timestamp)
        VALUES 
            (v_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_dashboard', null, '/dashboard', 'direct', NOW() - INTERVAL '2 hours'),
            (v_user_id, 'sample_session_1', 'navigation', 'page_view', 'visited_projects', '/dashboard', '/projects', 'click', NOW() - INTERVAL '1 hour'),
            (v_user_id, 'sample_session_1', 'interaction', 'click', 'created_project', '/projects', '/projects', 'click', NOW() - INTERVAL '45 minutes')
        ON CONFLICT DO NOTHING;
    EXCEPTION
        WHEN undefined_table THEN
            -- Table doesn't exist, skip
            NULL;
    END;

    -- Insert sample learning progress (only if table exists)
    BEGIN
        INSERT INTO learning_progress (user_id, course_id, course_provider, course_title, status, completion_percentage, time_spent_minutes, started_at, last_accessed_at)
        VALUES 
            (v_user_id, 'react-fundamentals', 'linkedin_learning', 'React.js Essential Training', 'in_progress', 65.0, 180, NOW() - INTERVAL '7 days', NOW() - INTERVAL '1 day'),
            (v_user_id, 'javascript-advanced', 'youtube', 'Advanced JavaScript Concepts', 'completed', 100.0, 240, NOW() - INTERVAL '14 days', NOW() - INTERVAL '5 days')
        ON CONFLICT DO NOTHING;
    EXCEPTION
        WHEN undefined_table THEN
            -- Table doesn't exist, skip
            NULL;
    END;

    -- Insert sample user skills (only if table exists)
    BEGIN
        INSERT INTO user_skills (user_id, name, level, proficiency_score, verified, verification_date, created_at)
        VALUES 
            (v_user_id, 'JavaScript', 3, 75, true, NOW(), NOW()),
            (v_user_id, 'React', 2, 60, false, null, NOW()),
            (v_user_id, 'CSS', 3, 80, true, NOW(), NOW())
        ON CONFLICT DO NOTHING;
    EXCEPTION
        WHEN undefined_table THEN
            -- Table doesn't exist, skip
            NULL;
    END;

    RAISE NOTICE 'Sample data populated successfully for user %', v_user_id;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION convert_task_to_gig(UUID, UUID, DECIMAL, DECIMAL, INTEGER, TEXT[], TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION populate_my_sample_data() TO authenticated;

-- Comments
COMMENT ON FUNCTION convert_task_to_gig IS 'Converts a task into a gig with proper relationship tracking (uses requester_id)';
COMMENT ON FUNCTION populate_my_sample_data IS 'Populates sample data for the currently authenticated user (safe - handles missing tables)';
