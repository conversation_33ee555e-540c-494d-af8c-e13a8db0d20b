/**
 * Supabase Authentication Context
 * 
 * Production-ready authentication context providing:
 * - User authentication state management
 * - Login/logout functionality
 * - Session persistence
 * - Error handling
 * - Loading states
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase, supabaseHelpers } from '../utils/supabase/supabase.utils';
import { handleRegistrationInvitations } from '../utils/registrationInvitationHandler';

// Create the context
const UserContext = createContext({
  currentUser: null,
  loading: true,
  signIn: () => {},
  signUp: () => {},
  signOut: () => {},
  resetPassword: () => {},
  updateProfile: () => {},
  error: null
});

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useAuth must be used within a UserProvider');
  }
  return context;
};

// Auth provider component
export const UserProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Clear error function
  const clearError = () => setError(null);

  // Fetch user data from database
  const fetchUserData = async (user) => {
    if (!user) return;

    try {
      console.log('Fetching user data for:', user.id);
      const { data, error } = await supabase
        .from('users')
        .select('display_name, avatar_url, is_premium, bio, social_links, stats')
        .eq('id', user.id)
        .single();

      if (error) {
        console.warn('Database error fetching user data:', error.message);
        // Always use fallback for any database error to prevent blocking
        const fallbackData = {
          display_name: user.user_metadata?.full_name || user.user_metadata?.name || user.email?.split('@')[0] || 'User',
          avatar_url: user.user_metadata?.avatar_url || null,
          is_premium: false
        };
        console.log('Using fallback user data:', fallbackData);
        setUserData(fallbackData);
      } else {
        console.log('Successfully fetched user data:', data);
        setUserData(data);
      }
    } catch (error) {
      console.error('Error in fetchUserData:', error);
      // Always provide fallback to prevent app from breaking
      const fallbackData = {
        display_name: user.user_metadata?.full_name || user.user_metadata?.name || user.email?.split('@')[0] || 'User',
        avatar_url: user.user_metadata?.avatar_url || null,
        is_premium: false
      };
      console.log('Using fallback user data after error:', fallbackData);
      setUserData(fallbackData);
    }
  };

  // Initialize auth state
  useEffect(() => {
    let mounted = true;

    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log('Getting initial session...');
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting session:', error);
          setError(error.message);
        } else if (mounted) {
          console.log('Initial session result:', session?.user?.id || 'No user');
          setCurrentUser(session?.user || null);

          // Fetch user data if user exists, but don't block loading
          if (session?.user) {
            fetchUserData(session.user).catch(err => {
              console.error('Error fetching user data in initial session:', err);
            });
          }
        }
      } catch (err) {
        console.error('Error in getInitialSession:', err);
        if (mounted) {
          setError(err.message);
        }
      } finally {
        if (mounted) {
          console.log('Setting loading to false');
          setLoading(false);
        }
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;

        console.log('Auth state changed:', event, session?.user?.id);

        setCurrentUser(session?.user || null);
        setLoading(false);
        setError(null);

        // Fetch user data when signed in, but don't block
        if (session?.user && event === 'SIGNED_IN') {
          fetchUserData(session.user).catch(err => {
            console.error('Error fetching user data on sign in:', err);
          });
        } else if (event === 'SIGNED_OUT') {
          setUserData(null);
        }

        // Handle specific auth events
        switch (event) {
          case 'SIGNED_IN':
            console.log('User signed in:', session?.user?.email);
            break;
          case 'SIGNED_OUT':
            console.log('User signed out');
            break;
          case 'TOKEN_REFRESHED':
            console.log('Token refreshed');
            break;
          case 'USER_UPDATED':
            console.log('User updated');
            break;
          default:
            break;
        }
      }
    );

    return () => {
      mounted = false;
      subscription?.unsubscribe();
    };
  }, []);

  // Sign in function
  const signIn = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await supabaseHelpers.signIn(email, password);
      return data;
    } catch (err) {
      console.error('Sign in error:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Sign up function
  const signUp = async (email, password, metadata = {}) => {
    try {
      setLoading(true);
      setError(null);

      const data = await supabaseHelpers.signUp(email, password, metadata);

      // If signup was successful and we have a user, check for pending invitations
      if (data?.user) {
        console.log('🎉 User registered successfully, checking for pending invitations...');

        // Handle pending invitations in the background
        // Don't await this to avoid blocking the signup flow
        handleRegistrationInvitations(data.user, email).catch(error => {
          console.error('Error handling registration invitations:', error);
          // Don't throw - invitation handling failure shouldn't break signup
        });
      }

      return data;
    } catch (err) {
      console.error('Sign up error:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await supabaseHelpers.signOut();
    } catch (err) {
      console.error('Sign out error:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Reset password function
  const resetPassword = async (email) => {
    try {
      setError(null);
      
      await supabaseHelpers.resetPassword(email);
      return { success: true };
    } catch (err) {
      console.error('Reset password error:', err);
      setError(err.message);
      throw err;
    }
  };

  // Update profile function
  const updateProfile = async (updates) => {
    try {
      setError(null);
      
      const data = await supabaseHelpers.updateProfile(updates);
      return data;
    } catch (err) {
      console.error('Update profile error:', err);
      setError(err.message);
      throw err;
    }
  };

  // OAuth sign in functions
  const loginWithGoogle = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) throw error;
      return data;
    } catch (err) {
      console.error('Google sign in error:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const loginWithGithub = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'github',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) throw error;
      return data;
    } catch (err) {
      console.error('GitHub sign in error:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value = {
    currentUser,
    userData,
    loading,
    isLoading: loading, // Alias for compatibility
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
    clearError,
    fetchUserData,
    // Aliases for compatibility with existing components
    login: signIn,
    signup: signUp,
    logout: signOut,
    loginWithGoogle,
    loginWithGithub,
    // Additional helper properties
    isAuthenticated: !!currentUser,
    userId: currentUser?.id,
    userEmail: currentUser?.email,
    userMetadata: currentUser?.user_metadata || {}
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

// Export the context for direct use if needed
export { UserContext };

// Default export
export default UserContext;
