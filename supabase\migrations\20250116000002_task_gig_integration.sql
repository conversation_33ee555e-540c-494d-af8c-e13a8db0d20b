-- Task-Gig Integration System
-- Allows tasks to be converted to gigs and vice versa

-- Task-Gig relationship tracking
CREATE TABLE task_gig_relationships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Relationship details
  task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
  gig_id UUID REFERENCES collaboration_requests(id) ON DELETE CASCADE,
  relationship_type TEXT NOT NULL CHECK (relationship_type IN ('task_to_gig', 'gig_to_task', 'bidirectional')),
  
  -- Conversion metadata
  converted_by UUID NOT NULL REFERENCES auth.users(id),
  conversion_reason TEXT,
  original_type TEXT NOT NULL CHECK (original_type IN ('task', 'gig')),
  
  -- Sync settings
  auto_sync_status BOOLEAN DEFAULT true,
  auto_sync_assignee B<PERSON><PERSON>EAN DEFAULT true,
  auto_sync_deadline BOOLEAN DEFAULT true,
  
  -- Status tracking
  is_active BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique relationships
  UNIQUE(task_id, gig_id)
);

-- Gig application to task assignment tracking
CREATE TABLE gig_task_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Assignment details
  gig_id UUID NOT NULL REFERENCES collaboration_requests(id) ON DELETE CASCADE,
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  application_id UUID REFERENCES collaboration_request_applications(id) ON DELETE SET NULL,
  
  -- Assignment metadata
  assigned_user_id UUID NOT NULL REFERENCES auth.users(id),
  assigned_by UUID NOT NULL REFERENCES auth.users(id),
  assignment_type TEXT DEFAULT 'gig_application' CHECK (assignment_type IN ('gig_application', 'direct_assignment', 'auto_assignment')),
  
  -- Terms and conditions
  agreed_rate DECIMAL(10,2),
  agreed_deadline TIMESTAMP WITH TIME ZONE,
  agreed_deliverables TEXT[],
  
  -- Status tracking
  status TEXT DEFAULT 'assigned' CHECK (status IN ('assigned', 'in_progress', 'completed', 'cancelled')),
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Task conversion templates for common gig types
CREATE TABLE task_gig_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Template details
  name TEXT NOT NULL,
  description TEXT,
  category TEXT, -- 'development', 'design', 'content', 'marketing', etc.
  
  -- Default gig settings
  default_budget_min DECIMAL(10,2),
  default_budget_max DECIMAL(10,2),
  default_timeline_days INTEGER,
  default_skill_requirements TEXT[],
  
  -- Template configuration
  required_fields TEXT[], -- Fields that must be filled when using template
  optional_fields TEXT[], -- Fields that can be customized
  
  -- Usage tracking
  usage_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced collaboration requests to support task integration
ALTER TABLE collaboration_requests ADD COLUMN IF NOT EXISTS source_task_id UUID REFERENCES tasks(id);
ALTER TABLE collaboration_requests ADD COLUMN IF NOT EXISTS auto_assign_on_acceptance BOOLEAN DEFAULT false;
ALTER TABLE collaboration_requests ADD COLUMN IF NOT EXISTS task_integration_settings JSONB DEFAULT '{}'::jsonb;

-- Enhanced tasks to support gig integration
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS source_gig_id UUID REFERENCES collaboration_requests(id);
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS is_gig_derived BOOLEAN DEFAULT false;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS gig_integration_settings JSONB DEFAULT '{}'::jsonb;

-- Indexes for performance
CREATE INDEX idx_task_gig_relationships_task_id ON task_gig_relationships(task_id);
CREATE INDEX idx_task_gig_relationships_gig_id ON task_gig_relationships(gig_id);
CREATE INDEX idx_task_gig_relationships_active ON task_gig_relationships(is_active) WHERE is_active = true;

CREATE INDEX idx_gig_task_assignments_gig_id ON gig_task_assignments(gig_id);
CREATE INDEX idx_gig_task_assignments_task_id ON gig_task_assignments(task_id);
CREATE INDEX idx_gig_task_assignments_user ON gig_task_assignments(assigned_user_id);
CREATE INDEX idx_gig_task_assignments_status ON gig_task_assignments(status);

CREATE INDEX idx_task_gig_templates_category ON task_gig_templates(category);
CREATE INDEX idx_task_gig_templates_active ON task_gig_templates(is_active) WHERE is_active = true;

CREATE INDEX idx_collaboration_requests_source_task ON collaboration_requests(source_task_id) WHERE source_task_id IS NOT NULL;
CREATE INDEX idx_tasks_source_gig ON tasks(source_gig_id) WHERE source_gig_id IS NOT NULL;

-- Functions for task-gig integration

-- Function to convert task to gig
CREATE OR REPLACE FUNCTION convert_task_to_gig(
  p_task_id UUID,
  p_user_id UUID,
  p_budget_min DECIMAL DEFAULT NULL,
  p_budget_max DECIMAL DEFAULT NULL,
  p_timeline_days INTEGER DEFAULT NULL,
  p_skill_requirements TEXT[] DEFAULT NULL,
  p_conversion_reason TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_task_record RECORD;
  v_gig_id UUID;
  v_relationship_id UUID;
BEGIN
  -- Get task details
  SELECT * INTO v_task_record
  FROM tasks
  WHERE id = p_task_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Task not found: %', p_task_id;
  END IF;
  
  -- Create collaboration request (gig)
  INSERT INTO collaboration_requests (
    title,
    description,
    project_id,
    requester_id,
    request_type,
    budget_min,
    budget_max,
    timeline_days,
    skill_requirements,
    status,
    source_task_id,
    auto_assign_on_acceptance,
    task_integration_settings
  ) VALUES (
    v_task_record.title,
    COALESCE(v_task_record.description, 'Converted from task'),
    v_task_record.project_id,
    p_user_id,
    'task_assistance',
    p_budget_min,
    p_budget_max,
    p_timeline_days,
    p_skill_requirements,
    'open',
    p_task_id,
    true,
    jsonb_build_object(
      'original_task_id', p_task_id,
      'auto_sync_enabled', true,
      'conversion_date', NOW()
    )
  ) RETURNING id INTO v_gig_id;
  
  -- Create relationship record
  INSERT INTO task_gig_relationships (
    task_id,
    gig_id,
    relationship_type,
    converted_by,
    conversion_reason,
    original_type
  ) VALUES (
    p_task_id,
    v_gig_id,
    'task_to_gig',
    p_user_id,
    p_conversion_reason,
    'task'
  ) RETURNING id INTO v_relationship_id;
  
  -- Update task to reference gig
  UPDATE tasks
  SET 
    source_gig_id = v_gig_id,
    is_gig_derived = false, -- This is the original task
    gig_integration_settings = jsonb_build_object(
      'converted_to_gig', true,
      'gig_id', v_gig_id,
      'relationship_id', v_relationship_id
    )
  WHERE id = p_task_id;
  
  RETURN v_gig_id;
END;
$$ LANGUAGE plpgsql;

-- Function to convert gig to task
CREATE OR REPLACE FUNCTION convert_gig_to_task(
  p_gig_id UUID,
  p_user_id UUID,
  p_project_id UUID,
  p_assignee_id UUID DEFAULT NULL,
  p_conversion_reason TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_gig_record RECORD;
  v_task_id UUID;
  v_relationship_id UUID;
BEGIN
  -- Get gig details
  SELECT * INTO v_gig_record
  FROM collaboration_requests
  WHERE id = p_gig_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Gig not found: %', p_gig_id;
  END IF;
  
  -- Create task
  INSERT INTO tasks (
    title,
    description,
    project_id,
    assignee_id,
    created_by,
    status,
    task_type,
    difficulty_level,
    estimated_hours,
    source_gig_id,
    is_gig_derived,
    gig_integration_settings
  ) VALUES (
    v_gig_record.title,
    COALESCE(v_gig_record.description, 'Converted from gig'),
    p_project_id,
    p_assignee_id,
    p_user_id,
    'todo',
    'development',
    'medium',
    COALESCE(v_gig_record.timeline_days * 8, 8), -- Estimate 8 hours per day
    p_gig_id,
    true,
    jsonb_build_object(
      'original_gig_id', p_gig_id,
      'auto_sync_enabled', true,
      'conversion_date', NOW()
    )
  ) RETURNING id INTO v_task_id;
  
  -- Create relationship record
  INSERT INTO task_gig_relationships (
    task_id,
    gig_id,
    relationship_type,
    converted_by,
    conversion_reason,
    original_type
  ) VALUES (
    v_task_id,
    p_gig_id,
    'gig_to_task',
    p_user_id,
    p_conversion_reason,
    'gig'
  ) RETURNING id INTO v_relationship_id;
  
  -- Update gig to reference task
  UPDATE collaboration_requests
  SET 
    source_task_id = v_task_id,
    task_integration_settings = jsonb_build_object(
      'converted_to_task', true,
      'task_id', v_task_id,
      'relationship_id', v_relationship_id
    )
  WHERE id = p_gig_id;
  
  RETURN v_task_id;
END;
$$ LANGUAGE plpgsql;

-- Function to sync task and gig status
CREATE OR REPLACE FUNCTION sync_task_gig_status()
RETURNS TRIGGER AS $$
DECLARE
  v_relationship RECORD;
BEGIN
  -- Handle task status changes
  IF TG_TABLE_NAME = 'tasks' THEN
    -- Find related gig
    SELECT * INTO v_relationship
    FROM task_gig_relationships
    WHERE task_id = NEW.id AND is_active = true AND auto_sync_status = true;
    
    IF FOUND THEN
      -- Update gig status based on task status
      UPDATE collaboration_requests
      SET status = CASE
        WHEN NEW.status = 'completed' THEN 'completed'
        WHEN NEW.status = 'in_progress' THEN 'in_progress'
        WHEN NEW.status = 'cancelled' THEN 'cancelled'
        ELSE 'open'
      END,
      updated_at = NOW()
      WHERE id = v_relationship.gig_id;
    END IF;
  END IF;
  
  -- Handle gig status changes
  IF TG_TABLE_NAME = 'collaboration_requests' THEN
    -- Find related task
    SELECT * INTO v_relationship
    FROM task_gig_relationships
    WHERE gig_id = NEW.id AND is_active = true AND auto_sync_status = true;
    
    IF FOUND THEN
      -- Update task status based on gig status
      UPDATE tasks
      SET status = CASE
        WHEN NEW.status = 'completed' THEN 'completed'
        WHEN NEW.status = 'in_progress' THEN 'in_progress'
        WHEN NEW.status = 'cancelled' THEN 'cancelled'
        ELSE 'todo'
      END,
      updated_at = NOW()
      WHERE id = v_relationship.task_id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to handle gig application acceptance
CREATE OR REPLACE FUNCTION handle_gig_application_acceptance()
RETURNS TRIGGER AS $$
DECLARE
  v_gig_record RECORD;
  v_task_id UUID;
BEGIN
  -- Only process when application is accepted
  IF NEW.status = 'accepted' AND OLD.status != 'accepted' THEN
    -- Get gig details
    SELECT * INTO v_gig_record
    FROM collaboration_requests
    WHERE id = NEW.request_id;
    
    -- If gig has auto_assign_on_acceptance and source_task_id
    IF v_gig_record.auto_assign_on_acceptance AND v_gig_record.source_task_id IS NOT NULL THEN
      -- Assign the task to the accepted applicant
      UPDATE tasks
      SET 
        assignee_id = NEW.applicant_id,
        status = 'in_progress',
        updated_at = NOW()
      WHERE id = v_gig_record.source_task_id;
      
      -- Create assignment record
      INSERT INTO gig_task_assignments (
        gig_id,
        task_id,
        application_id,
        assigned_user_id,
        assigned_by,
        assignment_type,
        status
      ) VALUES (
        NEW.request_id,
        v_gig_record.source_task_id,
        NEW.id,
        NEW.applicant_id,
        v_gig_record.requested_by,
        'gig_application',
        'assigned'
      );
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers
CREATE TRIGGER trigger_sync_task_status
  AFTER UPDATE OF status ON tasks
  FOR EACH ROW EXECUTE FUNCTION sync_task_gig_status();

CREATE TRIGGER trigger_sync_gig_status
  AFTER UPDATE OF status ON collaboration_requests
  FOR EACH ROW EXECUTE FUNCTION sync_task_gig_status();

CREATE TRIGGER trigger_handle_gig_application_acceptance
  AFTER UPDATE OF status ON collaboration_request_applications
  FOR EACH ROW EXECUTE FUNCTION handle_gig_application_acceptance();

-- Update timestamps trigger
CREATE TRIGGER update_task_gig_relationships_updated_at
  BEFORE UPDATE ON task_gig_relationships
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gig_task_assignments_updated_at
  BEFORE UPDATE ON gig_task_assignments
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_gig_templates_updated_at
  BEFORE UPDATE ON task_gig_templates
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS)
ALTER TABLE task_gig_relationships ENABLE ROW LEVEL SECURITY;
ALTER TABLE gig_task_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_gig_templates ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Task-gig relationships: Users can view relationships for their tasks/gigs
CREATE POLICY "Users can view task-gig relationships for their content" ON task_gig_relationships
  FOR SELECT USING (
    task_id IN (SELECT id FROM tasks WHERE assignee_id = auth.uid() OR created_by = auth.uid()) OR
    gig_id IN (SELECT id FROM collaboration_requests WHERE requested_by = auth.uid())
  );

CREATE POLICY "Users can create task-gig relationships for their content" ON task_gig_relationships
  FOR INSERT WITH CHECK (
    converted_by = auth.uid() AND (
      task_id IN (SELECT id FROM tasks WHERE assignee_id = auth.uid() OR created_by = auth.uid()) OR
      gig_id IN (SELECT id FROM collaboration_requests WHERE requested_by = auth.uid())
    )
  );

-- Gig-task assignments: Users can view assignments they're involved in
CREATE POLICY "Users can view relevant gig-task assignments" ON gig_task_assignments
  FOR SELECT USING (
    assigned_user_id = auth.uid() OR
    assigned_by = auth.uid() OR
    gig_id IN (SELECT id FROM collaboration_requests WHERE requested_by = auth.uid()) OR
    task_id IN (SELECT id FROM tasks WHERE assignee_id = auth.uid() OR created_by = auth.uid())
  );

-- Templates: Public read access
CREATE POLICY "Anyone can view active templates" ON task_gig_templates
  FOR SELECT USING (is_active = true);

-- Comments
COMMENT ON TABLE task_gig_relationships IS 'Tracks relationships between tasks and gigs for bidirectional integration';
COMMENT ON TABLE gig_task_assignments IS 'Manages assignments when gig applications are accepted';
COMMENT ON TABLE task_gig_templates IS 'Templates for converting tasks to gigs with predefined settings';
COMMENT ON FUNCTION convert_task_to_gig IS 'Converts a task into a gig with proper relationship tracking';
COMMENT ON FUNCTION convert_gig_to_task IS 'Converts a gig into a task with proper relationship tracking';
COMMENT ON FUNCTION sync_task_gig_status IS 'Automatically syncs status between related tasks and gigs';
