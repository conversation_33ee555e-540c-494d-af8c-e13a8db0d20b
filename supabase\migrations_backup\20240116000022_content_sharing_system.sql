-- Content Sharing System Migration
-- Enables sharing of learning content between users with social features

-- Create content_shares table
CREATE TABLE IF NOT EXISTS content_shares (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Share metadata
  shared_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content_type TEXT NOT NULL CHECK (content_type IN ('video', 'learning_path', 'progress', 'achievement')),
  content_id UUID NOT NULL, -- References various content tables
  content_title TEXT NOT NULL,
  content_url TEXT NOT NULL,
  
  -- Share details
  message TEXT,
  include_progress BOOLEAN DEFAULT false,
  is_public BOOLEAN DEFAULT true,
  allow_comments BOOLEAN DEFAULT true,
  
  -- Recipients (for direct shares)
  recipients UUID[] DEFAULT '{}',
  
  -- Engagement metrics
  view_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0,
  comment_count INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE -- Optional expiration
);

-- Create share_views table for tracking who viewed shared content
CREATE TABLE IF NOT EXISTS share_views (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Relationships
  share_id UUID NOT NULL REFERENCES content_shares(id) ON DELETE CASCADE,
  viewer_id UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- NULL for anonymous views
  
  -- View details
  viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT,
  referrer TEXT,
  
  -- Prevent duplicate views from same user
  UNIQUE(share_id, viewer_id)
);

-- Create share_likes table
CREATE TABLE IF NOT EXISTS share_likes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Relationships
  share_id UUID NOT NULL REFERENCES content_shares(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Prevent duplicate likes
  UNIQUE(share_id, user_id)
);

-- Create share_comments table
CREATE TABLE IF NOT EXISTS share_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Relationships
  share_id UUID NOT NULL REFERENCES content_shares(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  parent_comment_id UUID REFERENCES share_comments(id) ON DELETE CASCADE, -- For replies
  
  -- Comment content
  content TEXT NOT NULL,
  is_edited BOOLEAN DEFAULT false,
  
  -- Moderation
  is_hidden BOOLEAN DEFAULT false,
  is_flagged BOOLEAN DEFAULT false,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_profiles table if it doesn't exist (for sharing features)
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,

  -- Profile information
  display_name TEXT,
  full_name TEXT,
  avatar_url TEXT,
  bio TEXT,
  
  -- Privacy settings
  allow_content_sharing BOOLEAN DEFAULT true,
  allow_progress_sharing BOOLEAN DEFAULT true,
  public_profile BOOLEAN DEFAULT true,
  
  -- Social features
  follower_count INTEGER DEFAULT 0,
  following_count INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_follows table for social features
CREATE TABLE IF NOT EXISTS user_follows (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Relationships
  follower_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  following_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Prevent self-follows and duplicates
  CHECK (follower_id != following_id),
  UNIQUE(follower_id, following_id)
);

-- Create notifications table for sharing notifications
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Recipient
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Notification details
  type TEXT NOT NULL CHECK (type IN ('content_share', 'comment', 'like', 'follow', 'achievement', 'system')),
  title TEXT NOT NULL,
  message TEXT,
  
  -- Related data
  data JSONB DEFAULT '{}',
  
  -- Status
  is_read BOOLEAN DEFAULT false,
  is_archived BOOLEAN DEFAULT false,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_content_shares_shared_by ON content_shares(shared_by);
CREATE INDEX IF NOT EXISTS idx_content_shares_content_type ON content_shares(content_type);
CREATE INDEX IF NOT EXISTS idx_content_shares_public ON content_shares(is_public) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_content_shares_recipients ON content_shares USING gin(recipients);
CREATE INDEX IF NOT EXISTS idx_content_shares_created_at ON content_shares(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_share_views_share_id ON share_views(share_id);
CREATE INDEX IF NOT EXISTS idx_share_views_viewer_id ON share_views(viewer_id);
CREATE INDEX IF NOT EXISTS idx_share_views_viewed_at ON share_views(viewed_at DESC);

CREATE INDEX IF NOT EXISTS idx_share_likes_share_id ON share_likes(share_id);
CREATE INDEX IF NOT EXISTS idx_share_likes_user_id ON share_likes(user_id);

CREATE INDEX IF NOT EXISTS idx_share_comments_share_id ON share_comments(share_id);
CREATE INDEX IF NOT EXISTS idx_share_comments_user_id ON share_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_share_comments_parent ON share_comments(parent_comment_id);

CREATE INDEX IF NOT EXISTS idx_user_follows_follower ON user_follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_user_follows_following ON user_follows(following_id);

CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_unread ON notifications(user_id, is_read) WHERE is_read = false;
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);

-- Create functions to update engagement metrics
CREATE OR REPLACE FUNCTION update_share_view_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE content_shares 
  SET view_count = (
    SELECT COUNT(*) 
    FROM share_views 
    WHERE share_id = NEW.share_id
  )
  WHERE id = NEW.share_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_share_like_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE content_shares 
  SET like_count = (
    SELECT COUNT(*) 
    FROM share_likes 
    WHERE share_id = COALESCE(NEW.share_id, OLD.share_id)
  )
  WHERE id = COALESCE(NEW.share_id, OLD.share_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_share_comment_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE content_shares 
  SET comment_count = (
    SELECT COUNT(*) 
    FROM share_comments 
    WHERE share_id = COALESCE(NEW.share_id, OLD.share_id) 
    AND is_hidden = false
  )
  WHERE id = COALESCE(NEW.share_id, OLD.share_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_user_follow_counts()
RETURNS TRIGGER AS $$
BEGIN
  -- Update follower count
  UPDATE profiles 
  SET follower_count = (
    SELECT COUNT(*) 
    FROM user_follows 
    WHERE following_id = COALESCE(NEW.following_id, OLD.following_id)
  )
  WHERE id = COALESCE(NEW.following_id, OLD.following_id);
  
  -- Update following count
  UPDATE profiles 
  SET following_count = (
    SELECT COUNT(*) 
    FROM user_follows 
    WHERE follower_id = COALESCE(NEW.follower_id, OLD.follower_id)
  )
  WHERE id = COALESCE(NEW.follower_id, OLD.follower_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS trigger_update_share_view_count ON share_views;
CREATE TRIGGER trigger_update_share_view_count
  AFTER INSERT ON share_views
  FOR EACH ROW
  EXECUTE FUNCTION update_share_view_count();

DROP TRIGGER IF EXISTS trigger_update_share_like_count ON share_likes;
CREATE TRIGGER trigger_update_share_like_count
  AFTER INSERT OR DELETE ON share_likes
  FOR EACH ROW
  EXECUTE FUNCTION update_share_like_count();

DROP TRIGGER IF EXISTS trigger_update_share_comment_count ON share_comments;
CREATE TRIGGER trigger_update_share_comment_count
  AFTER INSERT OR UPDATE OR DELETE ON share_comments
  FOR EACH ROW
  EXECUTE FUNCTION update_share_comment_count();

DROP TRIGGER IF EXISTS trigger_update_user_follow_counts ON user_follows;
CREATE TRIGGER trigger_update_user_follow_counts
  AFTER INSERT OR DELETE ON user_follows
  FOR EACH ROW
  EXECUTE FUNCTION update_user_follow_counts();

-- Create comprehensive sharing analytics view
CREATE OR REPLACE VIEW sharing_analytics AS
SELECT
  cs.id,
  cs.shared_by,
  cs.content_type,
  cs.content_title,
  cs.is_public,
  cs.view_count,
  cs.like_count,
  cs.comment_count,
  cs.created_at,
  COALESCE(p.display_name, 'Unknown User') as sharer_name,
  p.avatar_url as sharer_avatar,
  CASE 
    WHEN cs.view_count > 0 THEN (cs.like_count::DECIMAL / cs.view_count) * 100
    ELSE 0 
  END as engagement_rate,
  array_length(cs.recipients, 1) as direct_share_count
FROM content_shares cs
LEFT JOIN profiles p ON p.id = cs.shared_by
WHERE cs.is_public = true OR cs.shared_by = auth.uid()
ORDER BY cs.created_at DESC;

-- Add RLS policies
ALTER TABLE content_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE share_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE share_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE share_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Content shares policies
CREATE POLICY "Users can view public shares and their own shares" ON content_shares
  FOR SELECT USING (
    is_public = true OR 
    shared_by = auth.uid() OR 
    auth.uid() = ANY(recipients)
  );

CREATE POLICY "Users can create their own shares" ON content_shares
  FOR INSERT WITH CHECK (shared_by = auth.uid());

CREATE POLICY "Users can update their own shares" ON content_shares
  FOR UPDATE USING (shared_by = auth.uid());

CREATE POLICY "Users can delete their own shares" ON content_shares
  FOR DELETE USING (shared_by = auth.uid());

-- Share views policies
CREATE POLICY "Anyone can view share views" ON share_views
  FOR SELECT USING (true);

CREATE POLICY "Users can record their own views" ON share_views
  FOR INSERT WITH CHECK (viewer_id = auth.uid() OR viewer_id IS NULL);

-- Share likes policies
CREATE POLICY "Anyone can view share likes" ON share_likes
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own likes" ON share_likes
  FOR ALL USING (user_id = auth.uid());

-- Share comments policies
CREATE POLICY "Anyone can view non-hidden comments" ON share_comments
  FOR SELECT USING (is_hidden = false);

CREATE POLICY "Users can create comments" ON share_comments
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own comments" ON share_comments
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own comments" ON share_comments
  FOR DELETE USING (user_id = auth.uid());

-- Profiles policies
CREATE POLICY "Anyone can view public profiles" ON profiles
  FOR SELECT USING (public_profile = true OR id = auth.uid());

CREATE POLICY "Users can update their own profile" ON profiles
  FOR ALL USING (id = auth.uid());

-- User follows policies
CREATE POLICY "Anyone can view follows" ON user_follows
  FOR SELECT USING (true);

CREATE POLICY "Users can manage their own follows" ON user_follows
  FOR ALL USING (follower_id = auth.uid());

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (user_id = auth.uid());

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON content_shares TO authenticated;
GRANT SELECT, INSERT ON share_views TO authenticated;
GRANT SELECT, INSERT, DELETE ON share_likes TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON share_comments TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON profiles TO authenticated;
GRANT SELECT, INSERT, DELETE ON user_follows TO authenticated;
GRANT SELECT, UPDATE ON notifications TO authenticated;
GRANT SELECT ON sharing_analytics TO authenticated;

-- Comments
COMMENT ON TABLE content_shares IS 'Shared learning content with engagement tracking';
COMMENT ON TABLE share_views IS 'View tracking for shared content';
COMMENT ON TABLE share_likes IS 'Like tracking for shared content';
COMMENT ON TABLE share_comments IS 'Comments on shared content with threading support';
COMMENT ON TABLE profiles IS 'User profiles with privacy settings for sharing';
COMMENT ON TABLE user_follows IS 'Social following relationships between users';
COMMENT ON TABLE notifications IS 'User notifications for sharing and social activities';
COMMENT ON VIEW sharing_analytics IS 'Analytics view for content sharing performance';
