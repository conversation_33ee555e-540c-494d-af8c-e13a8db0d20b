import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import {
  Card,
  CardBody,
  CardHeader,
  Input,
  Button,
  Select,
  SelectItem,
  Checkbox,
  Textarea,
  Avatar,
  Chip,
  Divider,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserPlus, Mail, Edit, Trash2, Users } from 'lucide-react';
import { generateInvitationToken, updateInvitationWithToken, generateInvitationLink } from '../../utils/invitationUtils';

const ContributorManager = ({ projectId, userRole, onContributorsUpdate }) => {
  const { currentUser } = useContext(UserContext);
  const [contributors, setContributors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  // Modal states
  const { isOpen: isAddModalOpen, onOpen: onAddModalOpen, onClose: onAddModalClose } = useDisclosure();
  const { isOpen: isEditModalOpen, onOpen: onEditModalOpen, onClose: onEditModalClose } = useDisclosure();
  const { isOpen: isBatchModalOpen, onOpen: onBatchModalOpen, onClose: onBatchModalClose } = useDisclosure();
  
  // Form states
  const [newContributor, setNewContributor] = useState({
    email: '',
    display_name: '',
    role: '',
    permission_level: 'Contributor',
    is_admin: false,
    address: '',
    state: '',
    county: '',
    title: '',
    is_company: false,
    company_name: '',
    signer_name: '',
    signer_title: ''
  });
  
  const [editingContributor, setEditingContributor] = useState(null);
  const [batchEmails, setBatchEmails] = useState('');

  // Permission levels
  const permissionLevels = [
    { key: 'Owner', label: 'Owner' },
    { key: 'Admin', label: 'Admin' },
    { key: 'Contributor', label: 'Contributor' },
    { key: 'Viewer', label: 'Viewer' }
  ];

  // Fetch contributors
  useEffect(() => {
    fetchContributors();
  }, [projectId]);

  const fetchContributors = async () => {
    if (!projectId) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('project_contributors')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setContributors(data || []);
      if (onContributorsUpdate) {
        onContributorsUpdate(data || []);
      }
    } catch (error) {
      console.error('Error fetching contributors:', error);
      toast.error('Failed to load contributors');
    } finally {
      setLoading(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setNewContributor({
      email: '',
      display_name: '',
      role: '',
      permission_level: 'Contributor',
      is_admin: false,
      address: '',
      state: '',
      county: '',
      title: '',
      is_company: false,
      company_name: '',
      signer_name: '',
      signer_title: ''
    });
  };

  // Send invitation email
  const sendInvitationEmail = async (contributorData) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        console.error('No session found for email sending');
        return;
      }

      // Generate secure invitation token
      const token = await generateInvitationToken(
        contributorData.email,
        'project',
        projectId
      );

      // Update invitation record with token
      await updateInvitationWithToken('project_contributors', contributorData.id, token);

      // Generate secure invitation link
      const invitationUrl = await generateInvitationLink(
        contributorData.id,
        contributorData.email,
        'project',
        projectId
      );

      // Get project details for email
      const { data: project } = await supabase
        .from('projects')
        .select('name, description')
        .eq('id', projectId)
        .single();

      // Get current user details
      const { data: { user } } = await supabase.auth.getUser();

      const emailVariables = {
        userName: contributorData.display_name || contributorData.email.split('@')[0],
        projectName: project?.name || 'Unknown Project',
        projectDescription: project?.description || 'No description available',
        role: contributorData.role || 'Contributor',
        inviterName: user?.user_metadata?.display_name || user?.email,
        invitationUrl
      };

      const response = await fetch('/.netlify/functions/email-service/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          to: contributorData.email,
          template: 'project_invitation',
          variables: emailVariables
        })
      });

      if (!response.ok) {
        throw new Error(`Email service responded with ${response.status}`);
      }

      console.log('Invitation email sent successfully');
    } catch (error) {
      console.error('Failed to send invitation email:', error);
      // Don't throw - email failure shouldn't break the invitation flow
    }
  };

  // Add contributor
  const addContributor = async () => {
    if (!newContributor.email) {
      toast.error('Email is required');
      return;
    }

    // Check if email already exists
    const emailExists = contributors.some(
      (contributor) => contributor.email === newContributor.email
    );

    if (emailExists) {
      toast.error('This email is already added as a contributor');
      return;
    }

    try {
      setSaving(true);

      const contributorData = {
        ...newContributor,
        project_id: projectId,
        status: 'pending',
        invitation_sent_at: new Date().toISOString(),
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('project_contributors')
        .insert([contributorData])
        .select()
        .single();

      if (error) throw error;

      // Send invitation email
      await sendInvitationEmail(data);

      setContributors([data, ...contributors]);
      resetForm();
      onAddModalClose();
      toast.success('Contributor added and invitation sent');

      if (onContributorsUpdate) {
        onContributorsUpdate([data, ...contributors]);
      }
    } catch (error) {
      console.error('Error adding contributor:', error);
      toast.error('Failed to add contributor');
    } finally {
      setSaving(false);
    }
  };

  // Update contributor
  const updateContributor = async () => {
    if (!editingContributor) return;

    try {
      setSaving(true);

      const { data, error } = await supabase
        .from('project_contributors')
        .update({
          display_name: editingContributor.display_name,
          role: editingContributor.role,
          permission_level: editingContributor.permission_level,
          is_admin: editingContributor.is_admin,
          address: editingContributor.address,
          state: editingContributor.state,
          county: editingContributor.county,
          title: editingContributor.title,
          is_company: editingContributor.is_company,
          company_name: editingContributor.company_name,
          signer_name: editingContributor.signer_name,
          signer_title: editingContributor.signer_title,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingContributor.id)
        .select()
        .single();

      if (error) throw error;

      const updatedContributors = contributors.map(c => 
        c.id === editingContributor.id ? data : c
      );
      setContributors(updatedContributors);
      setEditingContributor(null);
      onEditModalClose();
      toast.success('Contributor updated successfully');

      if (onContributorsUpdate) {
        onContributorsUpdate(updatedContributors);
      }
    } catch (error) {
      console.error('Error updating contributor:', error);
      toast.error('Failed to update contributor');
    } finally {
      setSaving(false);
    }
  };

  // Remove contributor
  const removeContributor = async (contributorId) => {
    if (!confirm('Are you sure you want to remove this contributor?')) return;

    try {
      setSaving(true);

      const { error } = await supabase
        .from('project_contributors')
        .delete()
        .eq('id', contributorId);

      if (error) throw error;

      const updatedContributors = contributors.filter(c => c.id !== contributorId);
      setContributors(updatedContributors);
      toast.success('Contributor removed successfully');

      if (onContributorsUpdate) {
        onContributorsUpdate(updatedContributors);
      }
    } catch (error) {
      console.error('Error removing contributor:', error);
      toast.error('Failed to remove contributor');
    } finally {
      setSaving(false);
    }
  };

  // Batch invite
  const handleBatchInvite = async () => {
    if (!batchEmails) {
      toast.error('Please enter at least one email');
      return;
    }

    // Split emails by comma, newline, or space
    const emails = batchEmails
      .split(/[,\n\s]+/)
      .map((email) => email.trim())
      .filter((email) => email);

    if (emails.length === 0) {
      toast.error('Please enter valid emails');
      return;
    }

    try {
      setSaving(true);

      const newContributors = [];
      const existingEmails = [];

      for (const email of emails) {
        // Skip if email is invalid
        if (!email.includes('@')) {
          toast.error(`Invalid email: ${email}`);
          continue;
        }

        // Skip if email already exists
        if (contributors.some((contributor) => contributor.email === email)) {
          existingEmails.push(email);
          continue;
        }

        // Add new contributor
        newContributors.push({
          email,
          display_name: '',
          role: '',
          permission_level: 'Contributor',
          is_admin: false,
          address: '',
          state: '',
          county: '',
          title: '',
          is_company: false,
          company_name: '',
          signer_name: '',
          signer_title: '',
          project_id: projectId,
          status: 'pending',
          invitation_sent_at: new Date().toISOString(),
          created_at: new Date().toISOString()
        });
      }

      if (newContributors.length === 0) {
        toast.error('All emails are already added as contributors');
        return;
      }

      const { data, error } = await supabase
        .from('project_contributors')
        .insert(newContributors)
        .select();

      if (error) throw error;

      // Send invitation emails for all new contributors
      const emailPromises = data.map(contributor => sendInvitationEmail(contributor));
      await Promise.allSettled(emailPromises); // Don't fail if some emails fail

      setContributors([...data, ...contributors]);
      setBatchEmails('');
      onBatchModalClose();
      toast.success(`Added ${newContributors.length} contributors and sent invitations`);

      if (existingEmails.length > 0) {
        toast.error(`${existingEmails.length} emails were already added`);
      }

      if (onContributorsUpdate) {
        onContributorsUpdate([...data, ...contributors]);
      }
    } catch (error) {
      console.error('Error batch inviting contributors:', error);
      toast.error('Failed to add contributors');
    } finally {
      setSaving(false);
    }
  };

  // Get contributor name
  const getContributorName = (contributor) => {
    return contributor.display_name || contributor.email?.split('@')[0] || 'Unknown';
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'inactive': return 'default';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-xl font-semibold text-foreground">Project Team</h3>
          <p className="text-default-500">Manage contributors and team members</p>
        </div>
        
        {userRole === 'admin' && (
          <div className="flex gap-2">
            <Button
              color="primary"
              variant="bordered"
              startContent={<Users size={16} />}
              onPress={onBatchModalOpen}
            >
              Batch Invite
            </Button>
            <Button
              color="primary"
              startContent={<UserPlus size={16} />}
              onPress={onAddModalOpen}
            >
              Add Contributor
            </Button>
          </div>
        )}
      </div>

      {/* Contributors List */}
      {contributors.length === 0 ? (
        <Card>
          <CardBody className="text-center py-12">
            <Users size={48} className="mx-auto text-default-300 mb-4" />
            <h4 className="text-lg font-medium text-default-600 mb-2">No team members yet</h4>
            <p className="text-default-500 mb-4">
              Add contributors to start collaborating on this project
            </p>
            {userRole === 'admin' && (
              <Button
                color="primary"
                startContent={<UserPlus size={16} />}
                onPress={onAddModalOpen}
              >
                Add First Contributor
              </Button>
            )}
          </CardBody>
        </Card>
      ) : (
        <div className="grid gap-4">
          {contributors.map((contributor, index) => (
            <motion.div
              key={contributor.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="border border-divider hover:border-primary/50 transition-colors">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <Avatar
                        src={contributor.avatar_url || '/default-avatar-specs.png'}
                        alt={`${getContributorName(contributor)}'s avatar`}
                        size="md"
                        className="flex-shrink-0"
                      />
                      
                      <div className="flex-grow">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-foreground">
                            {getContributorName(contributor)}
                          </h4>
                          {contributor.is_admin && (
                            <Chip size="sm" color="warning" variant="flat">
                              Admin
                            </Chip>
                          )}
                          <Chip 
                            size="sm" 
                            color={getStatusColor(contributor.status)} 
                            variant="flat"
                          >
                            {contributor.status || 'pending'}
                          </Chip>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-default-500">
                          <span className="flex items-center gap-1">
                            <Mail size={14} />
                            {contributor.email}
                          </span>
                          {contributor.role && (
                            <span>{contributor.role}</span>
                          )}
                          <span>{contributor.permission_level}</span>
                        </div>
                      </div>
                    </div>

                    {userRole === 'admin' && contributor.user_id !== currentUser?.id && (
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="light"
                          isIconOnly
                          onPress={() => {
                            setEditingContributor(contributor);
                            onEditModalOpen();
                          }}
                        >
                          <Edit size={16} />
                        </Button>
                        <Button
                          size="sm"
                          variant="light"
                          color="danger"
                          isIconOnly
                          onPress={() => removeContributor(contributor.id)}
                        >
                          <Trash2 size={16} />
                        </Button>
                      </div>
                    )}
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>
      )}

      {/* Add Contributor Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={onAddModalClose}
        size="2xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>Add New Contributor</ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Email"
                  placeholder="<EMAIL>"
                  value={newContributor.email}
                  onChange={(e) => setNewContributor({...newContributor, email: e.target.value})}
                  isRequired
                  type="email"
                />
                <Input
                  label="Display Name"
                  placeholder="John Doe"
                  value={newContributor.display_name}
                  onChange={(e) => setNewContributor({...newContributor, display_name: e.target.value})}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Role"
                  placeholder="Developer, Designer, etc."
                  value={newContributor.role}
                  onChange={(e) => setNewContributor({...newContributor, role: e.target.value})}
                />
                <Select
                  label="Permission Level"
                  selectedKeys={[newContributor.permission_level]}
                  onSelectionChange={(keys) => setNewContributor({...newContributor, permission_level: Array.from(keys)[0]})}
                >
                  {permissionLevels.map((level) => (
                    <SelectItem key={level.key} value={level.key}>
                      {level.label}
                    </SelectItem>
                  ))}
                </Select>
              </div>

              <Checkbox
                isSelected={newContributor.is_admin}
                onValueChange={(checked) => setNewContributor({...newContributor, is_admin: checked})}
              >
                Grant admin privileges
              </Checkbox>

              <Divider />

              <div className="space-y-4">
                <h4 className="font-medium">Contact Information (Optional)</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Address"
                    value={newContributor.address}
                    onChange={(e) => setNewContributor({...newContributor, address: e.target.value})}
                  />
                  <Input
                    label="State"
                    value={newContributor.state}
                    onChange={(e) => setNewContributor({...newContributor, state: e.target.value})}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="County"
                    value={newContributor.county}
                    onChange={(e) => setNewContributor({...newContributor, county: e.target.value})}
                  />
                  <Input
                    label="Title"
                    value={newContributor.title}
                    onChange={(e) => setNewContributor({...newContributor, title: e.target.value})}
                  />
                </div>
              </div>

              <Divider />

              <div className="space-y-4">
                <Checkbox
                  isSelected={newContributor.is_company}
                  onValueChange={(checked) => setNewContributor({...newContributor, is_company: checked})}
                >
                  This is a company/organization
                </Checkbox>

                {newContributor.is_company && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Company Name"
                      value={newContributor.company_name}
                      onChange={(e) => setNewContributor({...newContributor, company_name: e.target.value})}
                    />
                    <Input
                      label="Signer Name"
                      value={newContributor.signer_name}
                      onChange={(e) => setNewContributor({...newContributor, signer_name: e.target.value})}
                    />
                    <Input
                      label="Signer Title"
                      value={newContributor.signer_title}
                      onChange={(e) => setNewContributor({...newContributor, signer_title: e.target.value})}
                      className="md:col-span-2"
                    />
                  </div>
                )}
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onAddModalClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={addContributor}
              isLoading={saving}
            >
              Add Contributor
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Edit Contributor Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={onEditModalClose}
        size="2xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>Edit Contributor</ModalHeader>
          <ModalBody>
            {editingContributor && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Email"
                    value={editingContributor.email}
                    isDisabled
                    description="Email cannot be changed"
                  />
                  <Input
                    label="Display Name"
                    placeholder="John Doe"
                    value={editingContributor.display_name || ''}
                    onChange={(e) => setEditingContributor({...editingContributor, display_name: e.target.value})}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Role"
                    placeholder="Developer, Designer, etc."
                    value={editingContributor.role || ''}
                    onChange={(e) => setEditingContributor({...editingContributor, role: e.target.value})}
                  />
                  <Select
                    label="Permission Level"
                    selectedKeys={[editingContributor.permission_level]}
                    onSelectionChange={(keys) => setEditingContributor({...editingContributor, permission_level: Array.from(keys)[0]})}
                  >
                    {permissionLevels.map((level) => (
                      <SelectItem key={level.key} value={level.key}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                <Checkbox
                  isSelected={editingContributor.is_admin}
                  onValueChange={(checked) => setEditingContributor({...editingContributor, is_admin: checked})}
                >
                  Grant admin privileges
                </Checkbox>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onEditModalClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={updateContributor}
              isLoading={saving}
            >
              Update Contributor
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Batch Invite Modal */}
      <Modal
        isOpen={isBatchModalOpen}
        onClose={onBatchModalClose}
        size="lg"
      >
        <ModalContent>
          <ModalHeader>Batch Invite Contributors</ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Textarea
                label="Email Addresses"
                placeholder="Enter multiple emails separated by commas, spaces, or new lines&#10;<EMAIL>, <EMAIL>&#10;<EMAIL>"
                value={batchEmails}
                onChange={(e) => setBatchEmails(e.target.value)}
                minRows={6}
                description="All contributors will be added with 'Contributor' permission level. You can edit individual permissions after adding them."
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onBatchModalClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleBatchInvite}
              isLoading={saving}
            >
              Invite All
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ContributorManager;
