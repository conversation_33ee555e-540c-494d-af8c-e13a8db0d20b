-- Add Studio Business Information Columns to Teams Table
-- Date: 2025-01-12
-- Purpose: Add missing columns for studio business information, legal entity details, and contact information

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- ADD BUSINESS INFORMATION COLUMNS TO TEAMS TABLE
-- ============================================================================

-- Add industry column for studio categorization
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS industry TEXT;

COMMENT ON COLUMN public.teams.industry IS 'Industry category for the studio (e.g., software, gaming, film, etc.)';

-- Add business model as JSONB for flexible business model information
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS business_model JSONB DEFAULT '{"type": "collaborative"}';

COMMENT ON COLUMN public.teams.business_model IS 'Business model information stored as JSON (type, revenue_model, etc.)';

-- Add legal entity information as JSONB
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS legal_entity_info JSONB DEFAULT '{}';

COMMENT ON COLUMN public.teams.legal_entity_info IS 'Legal entity information (business_type, legal_name, company_type, tax_id, incorporation details)';

-- Add business address as JSONB
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS business_address JSONB DEFAULT '{}';

COMMENT ON COLUMN public.teams.business_address IS 'Business address information (address, city, state, zip, county, country)';

-- Add contact information as JSONB
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS contact_information JSONB DEFAULT '{}';

COMMENT ON COLUMN public.teams.contact_information IS 'Contact information (primary_email, primary_phone, signer_name, signer_title)';

-- ============================================================================
-- CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Index for industry searches
CREATE INDEX IF NOT EXISTS idx_teams_industry ON public.teams(industry) WHERE industry IS NOT NULL;

-- GIN indexes for JSONB columns to enable efficient queries
CREATE INDEX IF NOT EXISTS idx_teams_business_model_gin ON public.teams USING GIN (business_model);
CREATE INDEX IF NOT EXISTS idx_teams_legal_entity_info_gin ON public.teams USING GIN (legal_entity_info);
CREATE INDEX IF NOT EXISTS idx_teams_business_address_gin ON public.teams USING GIN (business_address);
CREATE INDEX IF NOT EXISTS idx_teams_contact_information_gin ON public.teams USING GIN (contact_information);

-- ============================================================================
-- UPDATE RLS POLICIES (if needed)
-- ============================================================================

-- The existing RLS policies should work fine with the new columns since they're part of the same table
-- No additional policies needed as the columns are just additional data for existing teams

-- ============================================================================
-- VALIDATION AND CONSTRAINTS
-- ============================================================================

-- Add check constraint for business model type
ALTER TABLE public.teams 
ADD CONSTRAINT IF NOT EXISTS teams_business_model_type_check 
CHECK (business_model ? 'type');

-- ============================================================================
-- MIGRATION COMPLETION LOG
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 Studio Business Information Migration Complete!';
    RAISE NOTICE '📋 Summary of changes:';
    RAISE NOTICE '   • Added industry column to teams table';
    RAISE NOTICE '   • Added business_model JSONB column to teams table';
    RAISE NOTICE '   • Added legal_entity_info JSONB column to teams table';
    RAISE NOTICE '   • Added business_address JSONB column to teams table';
    RAISE NOTICE '   • Added contact_information JSONB column to teams table';
    RAISE NOTICE '   • Created indexes for all new columns';
    RAISE NOTICE '   • Added validation constraints';
    RAISE NOTICE '✅ Studio creation should now work with full business information!';
END $$;
