-- Create bug_reports table for tracking user-reported bugs
CREATE TABLE IF NOT EXISTS public.bug_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'acknowledged', 'in-progress', 'fixed', 'wont-fix')),
    reported_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    acknowledged_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    fixed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    fixed_at TIMESTAMP WITH TIME ZONE,
    solution TEXT,
    is_public BOOLEAN DEFAULT false,
    severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    affected_area TEXT,
    browser_info TEXT,
    device_info TEXT,
    steps_to_reproduce TEXT,
    expected_behavior TEXT,
    actual_behavior TEXT,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Add comments to the table and columns
COMMENT ON TABLE public.bug_reports IS 'Table for tracking user-reported bugs';
COMMENT ON COLUMN public.bug_reports.id IS 'Unique identifier for the bug report';
COMMENT ON COLUMN public.bug_reports.title IS 'Short title describing the bug';
COMMENT ON COLUMN public.bug_reports.description IS 'Detailed description of the bug';
COMMENT ON COLUMN public.bug_reports.status IS 'Current status of the bug (open, acknowledged, in-progress, fixed, wont-fix)';
COMMENT ON COLUMN public.bug_reports.reported_by IS 'User who reported the bug';
COMMENT ON COLUMN public.bug_reports.acknowledged_by IS 'Admin who acknowledged the bug';
COMMENT ON COLUMN public.bug_reports.fixed_by IS 'Admin who fixed the bug';
COMMENT ON COLUMN public.bug_reports.created_at IS 'When the bug was reported';
COMMENT ON COLUMN public.bug_reports.updated_at IS 'When the bug report was last updated';
COMMENT ON COLUMN public.bug_reports.acknowledged_at IS 'When the bug was acknowledged';
COMMENT ON COLUMN public.bug_reports.fixed_at IS 'When the bug was fixed';
COMMENT ON COLUMN public.bug_reports.solution IS 'Description of how the bug was fixed';
COMMENT ON COLUMN public.bug_reports.is_public IS 'Whether the bug report is visible to all users';
COMMENT ON COLUMN public.bug_reports.severity IS 'Severity level of the bug';
COMMENT ON COLUMN public.bug_reports.affected_area IS 'Area of the application affected by the bug';
COMMENT ON COLUMN public.bug_reports.browser_info IS 'Browser information when the bug was encountered';
COMMENT ON COLUMN public.bug_reports.device_info IS 'Device information when the bug was encountered';
COMMENT ON COLUMN public.bug_reports.steps_to_reproduce IS 'Steps to reproduce the bug';
COMMENT ON COLUMN public.bug_reports.expected_behavior IS 'What was expected to happen';
COMMENT ON COLUMN public.bug_reports.actual_behavior IS 'What actually happened';
COMMENT ON COLUMN public.bug_reports.metadata IS 'Additional metadata about the bug';

-- Enable Row Level Security
ALTER TABLE public.bug_reports ENABLE ROW LEVEL SECURITY;

-- Create policies for bug_reports table
-- Anyone can view public bug reports
CREATE POLICY "Anyone can view public bug reports"
ON public.bug_reports
FOR SELECT
USING (is_public = true);

-- Users can view their own bug reports
CREATE POLICY "Users can view their own bug reports"
ON public.bug_reports
FOR SELECT
USING (reported_by = auth.uid());

-- Users can create bug reports
CREATE POLICY "Users can create bug reports"
ON public.bug_reports
FOR INSERT
WITH CHECK (auth.uid() IS NOT NULL);

-- Users can update their own bug reports if they're not fixed
CREATE POLICY "Users can update their own bug reports"
ON public.bug_reports
FOR UPDATE
USING (reported_by = auth.uid() AND status NOT IN ('fixed', 'wont-fix'))
WITH CHECK (reported_by = auth.uid() AND status NOT IN ('fixed', 'wont-fix'));

-- Admins can view all bug reports
CREATE POLICY "Admins can view all bug reports"
ON public.bug_reports
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE users.id = auth.uid() AND users.is_admin = true
    )
);

-- Admins can update all bug reports
CREATE POLICY "Admins can update all bug reports"
ON public.bug_reports
FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE users.id = auth.uid() AND users.is_admin = true
    )
)
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE users.id = auth.uid() AND users.is_admin = true
    )
);

-- Admins can delete bug reports
CREATE POLICY "Admins can delete bug reports"
ON public.bug_reports
FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE users.id = auth.uid() AND users.is_admin = true
    )
);
