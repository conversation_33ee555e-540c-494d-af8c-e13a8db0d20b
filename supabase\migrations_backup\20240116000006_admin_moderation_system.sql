-- Migration: Admin & Moderation System Enhancement
-- Description: Comprehensive admin tools, content moderation, and platform oversight
-- Created: 2024-01-16
-- Integration & Services Agent

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Admin roles and permissions table
CREATE TABLE admin_roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  role_name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  permissions JSONB NOT NULL DEFAULT '{}'::jsonb,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User admin assignments table
CREATE TABLE user_admin_roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role_id UUID NOT NULL REFERENCES admin_roles(id) ON DELETE CASCADE,
  assigned_by UUID NOT NULL REFERENCES auth.users(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  
  UNIQUE(user_id, role_id)
);

-- Admin actions log table
CREATE TABLE admin_actions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  admin_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  action_type VARCHAR(100) NOT NULL, -- 'user_suspend', 'content_remove', 'role_assign', etc.
  target_type VARCHAR(50) NOT NULL, -- 'user', 'project', 'alliance', 'content', etc.
  target_id UUID,
  reason TEXT,
  details JSONB DEFAULT '{}'::jsonb,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content moderation queue table
CREATE TABLE moderation_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  content_type VARCHAR(50) NOT NULL, -- 'project', 'profile', 'comment', 'alliance', etc.
  content_id UUID NOT NULL,
  content_data JSONB DEFAULT '{}'::jsonb, -- Snapshot of content at time of flagging
  flagged_by UUID REFERENCES auth.users(id),
  flag_reason VARCHAR(100) NOT NULL,
  flag_details TEXT,
  flag_category VARCHAR(50), -- 'spam', 'inappropriate', 'harassment', 'copyright', etc.
  priority INTEGER DEFAULT 3, -- 1=urgent, 2=high, 3=normal, 4=low, 5=info
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'approved', 'removed', 'edited', 'escalated'
  reviewed_by UUID REFERENCES auth.users(id),
  review_notes TEXT,
  review_action VARCHAR(50), -- 'approve', 'remove', 'edit', 'warn_user', 'suspend_user'
  auto_flagged BOOLEAN DEFAULT false,
  confidence_score DECIMAL(3,2), -- For auto-flagged content
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reviewed_at TIMESTAMP WITH TIME ZONE
);

-- Support tickets table
CREATE TABLE support_tickets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  ticket_number VARCHAR(20) UNIQUE NOT NULL, -- Human-readable ticket ID
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  subject VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  category VARCHAR(50) NOT NULL, -- 'technical', 'billing', 'account', 'feature_request', 'bug_report'
  priority VARCHAR(20) DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent', 'critical'
  status VARCHAR(20) DEFAULT 'open', -- 'open', 'in_progress', 'waiting_user', 'resolved', 'closed'
  assigned_to UUID REFERENCES auth.users(id),
  tags TEXT[],
  resolution_notes TEXT,
  satisfaction_rating INTEGER CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
  satisfaction_feedback TEXT,
  first_response_at TIMESTAMP WITH TIME ZONE,
  resolved_at TIMESTAMP WITH TIME ZONE,
  closed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Support ticket messages table
CREATE TABLE support_ticket_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  ticket_id UUID NOT NULL REFERENCES support_tickets(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  sender_type VARCHAR(20) NOT NULL, -- 'user', 'admin', 'system'
  message TEXT NOT NULL,
  attachments JSONB DEFAULT '[]'::jsonb,
  is_internal BOOLEAN DEFAULT false, -- Internal admin notes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System monitoring metrics table
CREATE TABLE system_monitoring (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  metric_type VARCHAR(50) NOT NULL, -- 'response_time', 'error_rate', 'uptime', 'user_count', etc.
  metric_name VARCHAR(100) NOT NULL,
  metric_value DECIMAL(12,4) NOT NULL,
  metric_unit VARCHAR(20), -- 'ms', 'percent', 'count', 'bytes', etc.
  threshold_warning DECIMAL(12,4),
  threshold_critical DECIMAL(12,4),
  status VARCHAR(20) DEFAULT 'normal', -- 'normal', 'warning', 'critical'
  source VARCHAR(50), -- 'application', 'database', 'external_api', etc.
  details JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Feature flags table
CREATE TABLE feature_flags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  flag_name VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  is_enabled BOOLEAN DEFAULT false,
  rollout_percentage INTEGER DEFAULT 0 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
  target_users UUID[], -- Specific users to enable for
  target_roles TEXT[], -- Specific roles to enable for
  environment VARCHAR(20) DEFAULT 'production', -- 'development', 'staging', 'production'
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User suspensions table
CREATE TABLE user_suspensions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  suspended_by UUID NOT NULL REFERENCES auth.users(id),
  reason TEXT NOT NULL,
  suspension_type VARCHAR(50) NOT NULL, -- 'temporary', 'permanent', 'warning'
  severity VARCHAR(20) NOT NULL, -- 'minor', 'moderate', 'major', 'severe'
  restrictions JSONB DEFAULT '{}'::jsonb, -- What the user can't do
  starts_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  appeal_submitted BOOLEAN DEFAULT false,
  appeal_notes TEXT,
  appeal_reviewed_by UUID REFERENCES auth.users(id),
  appeal_decision VARCHAR(20), -- 'upheld', 'reduced', 'overturned'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Platform announcements table
CREATE TABLE platform_announcements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  announcement_type VARCHAR(50) NOT NULL, -- 'maintenance', 'feature', 'policy', 'emergency'
  priority VARCHAR(20) DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
  target_audience VARCHAR(50) DEFAULT 'all', -- 'all', 'admins', 'users', 'specific_roles'
  target_roles TEXT[],
  is_published BOOLEAN DEFAULT false,
  show_banner BOOLEAN DEFAULT false,
  banner_color VARCHAR(20) DEFAULT 'blue', -- 'blue', 'green', 'yellow', 'red'
  published_by UUID NOT NULL REFERENCES auth.users(id),
  published_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default admin roles
INSERT INTO admin_roles (role_name, description, permissions) VALUES
('super_admin', 'Full platform access and control', '{
  "user_management": true,
  "content_moderation": true,
  "system_monitoring": true,
  "financial_oversight": true,
  "feature_management": true,
  "support_management": true,
  "admin_management": true,
  "platform_configuration": true
}'::jsonb),
('platform_admin', 'User and content management', '{
  "user_management": true,
  "content_moderation": true,
  "support_management": true,
  "view_analytics": true
}'::jsonb),
('support_admin', 'Customer support and ticket management', '{
  "support_management": true,
  "view_user_profiles": true,
  "basic_user_actions": true
}'::jsonb),
('financial_admin', 'Payment and financial oversight', '{
  "financial_oversight": true,
  "transaction_monitoring": true,
  "dispute_resolution": true,
  "view_analytics": true
}'::jsonb),
('content_moderator', 'Content review and moderation', '{
  "content_moderation": true,
  "view_user_profiles": true,
  "basic_user_actions": true
}'::jsonb);

-- Create indexes for performance
CREATE INDEX idx_admin_actions_admin_id ON admin_actions(admin_id);
CREATE INDEX idx_admin_actions_action_type ON admin_actions(action_type);
CREATE INDEX idx_admin_actions_target_type ON admin_actions(target_type);
CREATE INDEX idx_admin_actions_created_at ON admin_actions(created_at);

CREATE INDEX idx_moderation_queue_status ON moderation_queue(status);
CREATE INDEX idx_moderation_queue_priority ON moderation_queue(priority);
CREATE INDEX idx_moderation_queue_content_type ON moderation_queue(content_type);
CREATE INDEX idx_moderation_queue_flagged_by ON moderation_queue(flagged_by);
CREATE INDEX idx_moderation_queue_created_at ON moderation_queue(created_at);

CREATE INDEX idx_support_tickets_status ON support_tickets(status);
CREATE INDEX idx_support_tickets_priority ON support_tickets(priority);
CREATE INDEX idx_support_tickets_assigned_to ON support_tickets(assigned_to);
CREATE INDEX idx_support_tickets_user_id ON support_tickets(user_id);
CREATE INDEX idx_support_tickets_created_at ON support_tickets(created_at);

CREATE INDEX idx_support_ticket_messages_ticket_id ON support_ticket_messages(ticket_id);
CREATE INDEX idx_support_ticket_messages_created_at ON support_ticket_messages(created_at);

CREATE INDEX idx_system_monitoring_metric_type ON system_monitoring(metric_type);
CREATE INDEX idx_system_monitoring_status ON system_monitoring(status);
CREATE INDEX idx_system_monitoring_created_at ON system_monitoring(created_at);

CREATE INDEX idx_feature_flags_flag_name ON feature_flags(flag_name);
CREATE INDEX idx_feature_flags_is_enabled ON feature_flags(is_enabled);
CREATE INDEX idx_feature_flags_environment ON feature_flags(environment);

CREATE INDEX idx_user_suspensions_user_id ON user_suspensions(user_id);
CREATE INDEX idx_user_suspensions_is_active ON user_suspensions(is_active);
CREATE INDEX idx_user_suspensions_expires_at ON user_suspensions(expires_at);

CREATE INDEX idx_platform_announcements_is_published ON platform_announcements(is_published);
CREATE INDEX idx_platform_announcements_target_audience ON platform_announcements(target_audience);
CREATE INDEX idx_platform_announcements_expires_at ON platform_announcements(expires_at);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_admin_roles_updated_at BEFORE UPDATE ON admin_roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_support_tickets_updated_at BEFORE UPDATE ON support_tickets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_feature_flags_updated_at BEFORE UPDATE ON feature_flags FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_platform_announcements_updated_at BEFORE UPDATE ON platform_announcements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE admin_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_admin_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE moderation_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_ticket_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_monitoring ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_suspensions ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_announcements ENABLE ROW LEVEL SECURITY;

-- RLS policies for admin access
CREATE POLICY "Admin roles viewable by admins" ON admin_roles FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_admin_roles uar 
    JOIN admin_roles ar ON uar.role_id = ar.id 
    WHERE uar.user_id = auth.uid() AND uar.is_active = true
  )
);

CREATE POLICY "User admin roles viewable by admins" ON user_admin_roles FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_admin_roles uar 
    JOIN admin_roles ar ON uar.role_id = ar.id 
    WHERE uar.user_id = auth.uid() AND uar.is_active = true
  )
);

CREATE POLICY "Admin actions viewable by admins" ON admin_actions FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_admin_roles uar 
    JOIN admin_roles ar ON uar.role_id = ar.id 
    WHERE uar.user_id = auth.uid() AND uar.is_active = true
  )
);

CREATE POLICY "Moderation queue viewable by moderators" ON moderation_queue FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_admin_roles uar 
    JOIN admin_roles ar ON uar.role_id = ar.id 
    WHERE uar.user_id = auth.uid() AND uar.is_active = true 
    AND (ar.permissions->>'content_moderation')::boolean = true
  )
);

CREATE POLICY "Support tickets viewable by support admins" ON support_tickets FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_admin_roles uar 
    JOIN admin_roles ar ON uar.role_id = ar.id 
    WHERE uar.user_id = auth.uid() AND uar.is_active = true 
    AND (ar.permissions->>'support_management')::boolean = true
  ) OR user_id = auth.uid()
);

CREATE POLICY "Support ticket messages viewable by support admins and ticket owners" ON support_ticket_messages FOR ALL USING (
  EXISTS (
    SELECT 1 FROM support_tickets st 
    WHERE st.id = ticket_id AND (
      st.user_id = auth.uid() OR 
      EXISTS (
        SELECT 1 FROM user_admin_roles uar 
        JOIN admin_roles ar ON uar.role_id = ar.id 
        WHERE uar.user_id = auth.uid() AND uar.is_active = true 
        AND (ar.permissions->>'support_management')::boolean = true
      )
    )
  )
);

CREATE POLICY "System monitoring viewable by admins" ON system_monitoring FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_admin_roles uar 
    JOIN admin_roles ar ON uar.role_id = ar.id 
    WHERE uar.user_id = auth.uid() AND uar.is_active = true 
    AND (ar.permissions->>'system_monitoring')::boolean = true
  )
);

CREATE POLICY "Feature flags manageable by feature admins" ON feature_flags FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_admin_roles uar 
    JOIN admin_roles ar ON uar.role_id = ar.id 
    WHERE uar.user_id = auth.uid() AND uar.is_active = true 
    AND (ar.permissions->>'feature_management')::boolean = true
  )
);

CREATE POLICY "User suspensions viewable by admins" ON user_suspensions FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_admin_roles uar 
    JOIN admin_roles ar ON uar.role_id = ar.id 
    WHERE uar.user_id = auth.uid() AND uar.is_active = true 
    AND (ar.permissions->>'user_management')::boolean = true
  ) OR user_id = auth.uid()
);

CREATE POLICY "Platform announcements viewable by all" ON platform_announcements FOR SELECT USING (is_published = true);
CREATE POLICY "Platform announcements manageable by admins" ON platform_announcements FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_admin_roles uar 
    JOIN admin_roles ar ON uar.role_id = ar.id 
    WHERE uar.user_id = auth.uid() AND uar.is_active = true 
    AND (ar.permissions->>'platform_configuration')::boolean = true
  )
);

-- Functions for admin operations
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TEXT AS $$
DECLARE
  ticket_num TEXT;
  counter INTEGER;
BEGIN
  -- Get current date in YYYYMMDD format
  SELECT TO_CHAR(NOW(), 'YYYYMMDD') INTO ticket_num;
  
  -- Get count of tickets created today
  SELECT COUNT(*) + 1 INTO counter
  FROM support_tickets 
  WHERE DATE(created_at) = CURRENT_DATE;
  
  -- Format: YYYYMMDD-NNNN
  ticket_num := ticket_num || '-' || LPAD(counter::TEXT, 4, '0');
  
  RETURN ticket_num;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate ticket numbers
CREATE OR REPLACE FUNCTION set_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.ticket_number IS NULL THEN
    NEW.ticket_number := generate_ticket_number();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_support_ticket_number 
  BEFORE INSERT ON support_tickets 
  FOR EACH ROW EXECUTE FUNCTION set_ticket_number();

-- Table comments
COMMENT ON TABLE admin_roles IS 'Admin role definitions with permissions';
COMMENT ON TABLE user_admin_roles IS 'User assignments to admin roles';
COMMENT ON TABLE admin_actions IS 'Audit log of all admin actions';
COMMENT ON TABLE moderation_queue IS 'Content flagged for moderation review';
COMMENT ON TABLE support_tickets IS 'User support tickets and help requests';
COMMENT ON TABLE support_ticket_messages IS 'Messages within support tickets';
COMMENT ON TABLE system_monitoring IS 'System health and performance metrics';
COMMENT ON TABLE feature_flags IS 'Feature toggle configuration';
COMMENT ON TABLE user_suspensions IS 'User account suspensions and restrictions';
COMMENT ON TABLE platform_announcements IS 'Platform-wide announcements and notices';
