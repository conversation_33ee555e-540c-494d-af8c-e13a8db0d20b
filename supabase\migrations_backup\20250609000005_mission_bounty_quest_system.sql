-- Mission, Bounty & Quest System Enhancement Migration
-- Backend Specialist: Support for enhanced mission system, bounty board, and quest framework

-- Add quest-specific columns to tasks table if they don't exist
ALTER TABLE public.tasks 
ADD COLUMN IF NOT EXISTS quest_type TEXT CHECK (quest_type IN ('skill', 'collaboration', 'achievement', 'exploration', 'social')),
ADD COLUMN IF NOT EXISTS quest_requirements JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS quest_rewards JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS difficulty_points INTEGER DEFAULT 0 CHECK (difficulty_points >= 0),
ADD COLUMN IF NOT EXISTS logged_hours DECIMAL(8,2) DEFAULT 0 CHECK (logged_hours >= 0),
ADD COLUMN IF NOT EXISTS task_type TEXT; -- 'development', 'design', 'marketing', etc.

-- Create user_quests table for quest progress tracking
CREATE TABLE IF NOT EXISTS public.user_quests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    quest_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'in_progress', 'completed', 'abandoned')),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    completed_at TIMESTAMP WITH TIME ZONE,
    progress_data JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Constraints
    UNIQUE(user_id, quest_id)
);

-- Create alliance_preferences table for alliance configuration
CREATE TABLE IF NOT EXISTS public.alliance_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    alliance_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
    invite_members_enabled BOOLEAN DEFAULT true,
    shared_workspace_enabled BOOLEAN DEFAULT true,
    auto_venture_creation BOOLEAN DEFAULT false,
    notification_settings JSONB DEFAULT '{}'::jsonb,
    privacy_settings JSONB DEFAULT '{}'::jsonb,
    collaboration_settings JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Constraints
    UNIQUE(alliance_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_tasks_quest_type ON public.tasks(quest_type) WHERE quest_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tasks_task_type ON public.tasks(task_type) WHERE task_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tasks_difficulty_points ON public.tasks(difficulty_points) WHERE difficulty_points > 0;
CREATE INDEX IF NOT EXISTS idx_tasks_bounty_amount ON public.tasks(bounty_amount) WHERE bounty_amount > 0;
CREATE INDEX IF NOT EXISTS idx_tasks_public_bounties ON public.tasks(task_category, is_public, status) WHERE task_category = 'bounty' AND is_public = true;

CREATE INDEX IF NOT EXISTS idx_user_quests_user_id ON public.user_quests(user_id);
CREATE INDEX IF NOT EXISTS idx_user_quests_quest_id ON public.user_quests(quest_id);
CREATE INDEX IF NOT EXISTS idx_user_quests_status ON public.user_quests(status);
CREATE INDEX IF NOT EXISTS idx_user_quests_active ON public.user_quests(user_id, status) WHERE status IN ('active', 'in_progress');

CREATE INDEX IF NOT EXISTS idx_bounty_applications_task_id ON public.bounty_applications(task_id);
CREATE INDEX IF NOT EXISTS idx_bounty_applications_applicant_id ON public.bounty_applications(applicant_id);
CREATE INDEX IF NOT EXISTS idx_bounty_applications_status ON public.bounty_applications(status);

CREATE INDEX IF NOT EXISTS idx_alliance_invitations_email ON public.alliance_invitations(email);
CREATE INDEX IF NOT EXISTS idx_alliance_invitations_alliance_id ON public.alliance_invitations(alliance_id);
CREATE INDEX IF NOT EXISTS idx_alliance_invitations_status ON public.alliance_invitations(status);

-- Enable Row Level Security
ALTER TABLE public.user_quests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alliance_preferences ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_quests
CREATE POLICY "Users can view their own quests" ON public.user_quests
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create their own quest records" ON public.user_quests
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own quest progress" ON public.user_quests
    FOR UPDATE USING (user_id = auth.uid());

-- RLS Policies for alliance_preferences
CREATE POLICY "Alliance members can view preferences" ON public.alliance_preferences
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = alliance_id AND tm.user_id = auth.uid() AND tm.status = 'active'
        )
    );

CREATE POLICY "Alliance admins can manage preferences" ON public.alliance_preferences
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = alliance_id AND tm.user_id = auth.uid() 
            AND tm.role IN ('founder', 'owner', 'admin') AND tm.status = 'active'
        )
    );

-- Update existing tasks to have default values for new columns
UPDATE public.tasks 
SET 
    difficulty_points = CASE 
        WHEN difficulty_level = 'easy' THEN 10
        WHEN difficulty_level = 'medium' THEN 25
        WHEN difficulty_level = 'hard' THEN 50
        WHEN difficulty_level = 'expert' THEN 100
        ELSE 25
    END
WHERE difficulty_points = 0 OR difficulty_points IS NULL;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to user_quests table
DROP TRIGGER IF EXISTS update_user_quests_updated_at ON public.user_quests;
CREATE TRIGGER update_user_quests_updated_at
    BEFORE UPDATE ON public.user_quests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Apply trigger to alliance_preferences table
DROP TRIGGER IF EXISTS update_alliance_preferences_updated_at ON public.alliance_preferences;
CREATE TRIGGER update_alliance_preferences_updated_at
    BEFORE UPDATE ON public.alliance_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE public.user_quests IS 'User quest progress tracking for gamified collaboration';
COMMENT ON TABLE public.alliance_preferences IS 'Alliance configuration and preference settings';

COMMENT ON COLUMN public.tasks.quest_type IS 'Type of quest: skill, collaboration, achievement, exploration, social';
COMMENT ON COLUMN public.tasks.quest_requirements IS 'JSON object defining quest completion requirements';
COMMENT ON COLUMN public.tasks.quest_rewards IS 'JSON object defining quest completion rewards';
COMMENT ON COLUMN public.tasks.difficulty_points IS 'Point value for task difficulty and completion';
COMMENT ON COLUMN public.tasks.logged_hours IS 'Actual hours logged for task completion';
COMMENT ON COLUMN public.tasks.task_type IS 'Task type category: development, design, marketing, etc.';

-- Insert default alliance preferences for existing alliances
INSERT INTO public.alliance_preferences (alliance_id, notification_settings, privacy_settings, collaboration_settings)
SELECT 
    id,
    '{"new_member_notifications": true, "venture_updates": true, "mission_assignments": true}'::jsonb,
    '{"public_member_list": true, "public_venture_list": false, "public_revenue_stats": false}'::jsonb,
    '{"auto_mission_assignment": false, "skill_based_recommendations": true, "cross_venture_collaboration": true}'::jsonb
FROM public.teams 
WHERE NOT EXISTS (
    SELECT 1 FROM public.alliance_preferences ap WHERE ap.alliance_id = teams.id
);

-- Create function to automatically create alliance preferences
CREATE OR REPLACE FUNCTION create_default_alliance_preferences()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.alliance_preferences (alliance_id)
    VALUES (NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-create preferences for new alliances
DROP TRIGGER IF EXISTS auto_create_alliance_preferences ON public.teams;
CREATE TRIGGER auto_create_alliance_preferences
    AFTER INSERT ON public.teams
    FOR EACH ROW
    EXECUTE FUNCTION create_default_alliance_preferences();
