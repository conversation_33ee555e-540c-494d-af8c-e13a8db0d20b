-- CRITICAL COMPLIANCE: Tax-Compliant Financial System
-- Day 1 - Developer 3: Financial transactions with full tax compliance

-- Create financial_transactions table with tax compliance
CREATE TABLE public.financial_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Company and Project Context
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
    team_id UUID REFERENCES public.teams(id) ON DELETE SET NULL,
    
    -- Transaction Core Data
    transaction_type TEXT NOT NULL CHECK (transaction_type IN (
        'commission', 'recurring_fee', 'royalty', 'expense', 'refund', 'bonus', 'salary'
    )),
    transaction_category TEXT DEFAULT 'business_payment' CHECK (transaction_category IN (
        'business_payment', 'contractor_payment', 'employee_payment', 'expense_reimbursement'
    )),
    
    -- Financial Amounts
    gross_amount DECIMAL(12,2) NOT NULL CHECK (gross_amount >= 0),
    tax_amount DECIMAL(12,2) DEFAULT 0 CHECK (tax_amount >= 0),
    net_amount DECIMAL(12,2) NOT NULL CHECK (net_amount >= 0),
    currency TEXT DEFAULT 'USD',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    
    -- Tax Compliance (CRITICAL for business operations)
    tax_category TEXT CHECK (tax_category IN ('1099-NEC', '1099-MISC', 'W2', 'exempt', 'international')),
    requires_1099 BOOLEAN DEFAULT false,
    requires_w2 BOOLEAN DEFAULT false,
    backup_withholding_rate DECIMAL(5,2) DEFAULT 0 CHECK (backup_withholding_rate >= 0 AND backup_withholding_rate <= 100),
    tax_year INTEGER DEFAULT EXTRACT(YEAR FROM CURRENT_DATE),
    
    -- Parties Involved
    payer_company_id UUID REFERENCES public.companies(id),
    payee_user_id UUID REFERENCES auth.users(id),
    payee_company_id UUID REFERENCES public.companies(id),
    
    -- Processing Status
    status TEXT DEFAULT 'pending' CHECK (status IN (
        'pending', 'approved', 'processing', 'paid', 'failed', 'cancelled', 'disputed'
    )),
    processed_at TIMESTAMP WITH TIME ZONE,
    payment_method TEXT CHECK (payment_method IN (
        'ach', 'wire', 'check', 'paypal', 'stripe', 'manual'
    )),
    external_transaction_id TEXT, -- For payment processor reference
    
    -- Approval Workflow (REQUIRED for businesses)
    approval_required BOOLEAN DEFAULT true,
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_notes TEXT,
    
    -- Description and Context
    description TEXT NOT NULL,
    reference_number TEXT, -- Invoice number, PO number, etc.
    
    -- Audit Trail
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create tax_documents table for 1099s and other tax forms
CREATE TABLE public.tax_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Company and Recipient
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    payee_user_id UUID NOT NULL REFERENCES auth.users(id),
    payee_company_id UUID REFERENCES public.companies(id),
    
    -- Document Details
    document_type TEXT NOT NULL CHECK (document_type IN ('1099-NEC', '1099-MISC', 'W2', '1042-S')),
    tax_year INTEGER NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL CHECK (total_amount >= 0),
    
    -- Tax Categories (for different boxes on forms)
    nonemployee_compensation DECIMAL(12,2) DEFAULT 0, -- Box 1 on 1099-NEC
    rents DECIMAL(12,2) DEFAULT 0, -- Box 1 on 1099-MISC
    royalties DECIMAL(12,2) DEFAULT 0, -- Box 2 on 1099-MISC
    other_income DECIMAL(12,2) DEFAULT 0, -- Box 3 on 1099-MISC
    backup_withholding DECIMAL(12,2) DEFAULT 0, -- Box 4
    
    -- Status and Processing
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'generated', 'sent', 'corrected')),
    generated_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    
    -- File Storage
    document_url TEXT, -- URL to generated PDF
    correction_of UUID REFERENCES public.tax_documents(id), -- If this is a correction
    
    -- Audit
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX idx_financial_transactions_company ON public.financial_transactions(company_id, tax_year);
CREATE INDEX idx_financial_transactions_payee ON public.financial_transactions(payee_user_id, tax_year);
CREATE INDEX idx_financial_transactions_1099 ON public.financial_transactions(requires_1099, tax_year) WHERE requires_1099 = true;
CREATE INDEX idx_financial_transactions_status ON public.financial_transactions(status, created_at);
CREATE INDEX idx_financial_transactions_approval ON public.financial_transactions(approval_required, approved_at) WHERE approval_required = true;

CREATE INDEX idx_tax_documents_company_year ON public.tax_documents(company_id, tax_year);
CREATE INDEX idx_tax_documents_payee_year ON public.tax_documents(payee_user_id, tax_year);
CREATE INDEX idx_tax_documents_type_year ON public.tax_documents(document_type, tax_year);

-- Enable RLS
ALTER TABLE public.financial_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tax_documents ENABLE ROW LEVEL SECURITY;

-- RLS Policies for financial_transactions
CREATE POLICY "Users can view transactions for their companies" ON public.financial_transactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.companies c
            JOIN public.teams t ON c.id = t.company_id
            JOIN public.team_members tm ON t.id = tm.team_id
            WHERE c.id = financial_transactions.company_id AND tm.user_id = auth.uid()
        ) OR
        payee_user_id = auth.uid() OR
        created_by = auth.uid()
    );

CREATE POLICY "Authorized users can create transactions" ON public.financial_transactions
    FOR INSERT WITH CHECK (
        created_by = auth.uid() AND
        EXISTS (
            SELECT 1 FROM public.companies c
            JOIN public.teams t ON c.id = t.company_id
            JOIN public.team_members tm ON t.id = tm.team_id
            WHERE c.id = financial_transactions.company_id 
            AND tm.user_id = auth.uid() 
            AND tm.is_admin = true
        )
    );

CREATE POLICY "Authorized users can update transactions" ON public.financial_transactions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.companies c
            JOIN public.teams t ON c.id = t.company_id
            JOIN public.team_members tm ON t.id = tm.team_id
            WHERE c.id = financial_transactions.company_id 
            AND tm.user_id = auth.uid() 
            AND tm.is_admin = true
        )
    );

-- RLS Policies for tax_documents
CREATE POLICY "Users can view tax documents for their companies or themselves" ON public.tax_documents
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.companies c
            JOIN public.teams t ON c.id = t.company_id
            JOIN public.team_members tm ON t.id = tm.team_id
            WHERE c.id = tax_documents.company_id AND tm.user_id = auth.uid()
        ) OR
        payee_user_id = auth.uid()
    );

-- Function to calculate tax requirements
CREATE OR REPLACE FUNCTION calculate_tax_requirements(
    p_amount DECIMAL,
    p_payee_user_id UUID,
    p_company_id UUID,
    p_transaction_type TEXT
) RETURNS JSONB AS $$
DECLARE
    result JSONB;
    annual_total DECIMAL;
    requires_1099 BOOLEAN := false;
    tax_category TEXT := 'exempt';
BEGIN
    -- Get annual total for this payee from this company
    SELECT COALESCE(SUM(gross_amount), 0) INTO annual_total
    FROM public.financial_transactions
    WHERE payee_user_id = p_payee_user_id
    AND company_id = p_company_id
    AND tax_year = EXTRACT(YEAR FROM CURRENT_DATE)
    AND status IN ('paid', 'approved');
    
    -- Add current amount
    annual_total := annual_total + p_amount;
    
    -- Determine 1099 requirements
    IF p_transaction_type IN ('commission', 'royalty') AND annual_total >= 600 THEN
        requires_1099 := true;
        tax_category := '1099-NEC';
    ELSIF p_transaction_type = 'recurring_fee' AND annual_total >= 600 THEN
        requires_1099 := true;
        tax_category := '1099-MISC';
    END IF;
    
    result := jsonb_build_object(
        'requires_1099', requires_1099,
        'tax_category', tax_category,
        'annual_total', annual_total,
        'threshold_met', annual_total >= 600
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to auto-calculate tax fields on insert/update
CREATE OR REPLACE FUNCTION auto_calculate_tax_fields()
RETURNS TRIGGER AS $$
DECLARE
    tax_calc JSONB;
BEGIN
    -- Calculate tax requirements
    tax_calc := calculate_tax_requirements(
        NEW.gross_amount,
        NEW.payee_user_id,
        NEW.company_id,
        NEW.transaction_type
    );
    
    -- Set tax fields
    NEW.requires_1099 := (tax_calc->>'requires_1099')::BOOLEAN;
    NEW.tax_category := tax_calc->>'tax_category';
    
    -- Calculate net amount (gross - tax withholding)
    NEW.tax_amount := NEW.gross_amount * (NEW.backup_withholding_rate / 100);
    NEW.net_amount := NEW.gross_amount - NEW.tax_amount;
    
    -- Set approval requirement for business transactions
    IF NEW.gross_amount >= 1000 OR NEW.requires_1099 THEN
        NEW.approval_required := true;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for auto-calculating tax fields
CREATE TRIGGER auto_calculate_tax_fields_trigger
    BEFORE INSERT OR UPDATE ON public.financial_transactions
    FOR EACH ROW EXECUTE FUNCTION auto_calculate_tax_fields();

-- Function to generate 1099 summary
CREATE OR REPLACE FUNCTION generate_1099_summary(
    p_company_id UUID,
    p_tax_year INTEGER DEFAULT NULL
) RETURNS TABLE (
    payee_user_id UUID,
    payee_email TEXT,
    payee_name TEXT,
    total_amount DECIMAL,
    nonemployee_compensation DECIMAL,
    other_income DECIMAL,
    backup_withholding DECIMAL,
    transaction_count INTEGER
) AS $$
BEGIN
    IF p_tax_year IS NULL THEN
        p_tax_year := EXTRACT(YEAR FROM CURRENT_DATE);
    END IF;
    
    RETURN QUERY
    SELECT 
        ft.payee_user_id,
        u.email as payee_email,
        COALESCE(u.raw_user_meta_data->>'full_name', u.email) as payee_name,
        SUM(ft.gross_amount) as total_amount,
        SUM(CASE WHEN ft.tax_category = '1099-NEC' THEN ft.gross_amount ELSE 0 END) as nonemployee_compensation,
        SUM(CASE WHEN ft.tax_category = '1099-MISC' THEN ft.gross_amount ELSE 0 END) as other_income,
        SUM(ft.tax_amount) as backup_withholding,
        COUNT(*)::INTEGER as transaction_count
    FROM public.financial_transactions ft
    JOIN auth.users u ON ft.payee_user_id = u.id
    WHERE ft.company_id = p_company_id
    AND ft.tax_year = p_tax_year
    AND ft.requires_1099 = true
    AND ft.status = 'paid'
    GROUP BY ft.payee_user_id, u.email, u.raw_user_meta_data->>'full_name'
    HAVING SUM(ft.gross_amount) >= 600
    ORDER BY SUM(ft.gross_amount) DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.financial_transactions TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.tax_documents TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_tax_requirements TO authenticated;
GRANT EXECUTE ON FUNCTION generate_1099_summary TO authenticated;

-- Add triggers for updated_at
CREATE TRIGGER update_financial_transactions_updated_at 
    BEFORE UPDATE ON public.financial_transactions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tax_documents_updated_at 
    BEFORE UPDATE ON public.tax_documents 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE public.financial_transactions IS 'Tax-compliant financial transactions with automatic 1099 tracking and approval workflows';
COMMENT ON COLUMN public.financial_transactions.requires_1099 IS 'Automatically calculated based on amount thresholds and transaction type';
COMMENT ON COLUMN public.financial_transactions.tax_category IS 'Determines which tax form is required (1099-NEC, 1099-MISC, etc.)';
COMMENT ON COLUMN public.financial_transactions.backup_withholding_rate IS 'Percentage rate for backup withholding (typically 24% for missing TIN)';

COMMENT ON TABLE public.tax_documents IS '1099 and other tax document generation and tracking';
COMMENT ON FUNCTION generate_1099_summary IS 'Generates summary data for 1099 form creation for a given company and tax year';
