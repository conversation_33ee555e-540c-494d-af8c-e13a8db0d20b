-- ============================================================================
-- ALLY & FRIEND REQUEST SYSTEM MIGRATION
-- ============================================================================
-- Creates comprehensive social networking infrastructure for user connections,
-- friend requests, privacy controls, and ally discovery features.

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- USER CONNECTIONS & ALLY RELATIONSHIPS
-- ============================================================================

-- Create or update user_allies table for managing friend/ally connections
DO $$
BEGIN
    -- Check if table exists and get its structure
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_allies') THEN
        -- Table exists, add missing columns if they don't exist

        -- Add connection_type column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'connection_type') THEN
            ALTER TABLE public.user_allies ADD COLUMN connection_type VARCHAR(20) DEFAULT 'friend' CHECK (connection_type IN ('friend', 'colleague', 'collaborator', 'mentor', 'mentee'));
        END IF;

        -- Add requested_at column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'requested_at') THEN
            ALTER TABLE public.user_allies ADD COLUMN requested_at TIMESTAMP WITH TIME ZONE DEFAULT now();
        END IF;

        -- Add accepted_at column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'accepted_at') THEN
            ALTER TABLE public.user_allies ADD COLUMN accepted_at TIMESTAMP WITH TIME ZONE;
        END IF;

        -- Add declined_at column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'declined_at') THEN
            ALTER TABLE public.user_allies ADD COLUMN declined_at TIMESTAMP WITH TIME ZONE;
        END IF;

        -- Add blocked_at column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'blocked_at') THEN
            ALTER TABLE public.user_allies ADD COLUMN blocked_at TIMESTAMP WITH TIME ZONE;
        END IF;

        -- Add created_by column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'created_by') THEN
            ALTER TABLE public.user_allies ADD COLUMN created_by UUID REFERENCES auth.users(id);
        END IF;

        -- Add request_message column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'request_message') THEN
            ALTER TABLE public.user_allies ADD COLUMN request_message TEXT;
        END IF;

        -- Add notes column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_allies' AND column_name = 'notes') THEN
            ALTER TABLE public.user_allies ADD COLUMN notes TEXT;
        END IF;

    ELSE
        -- Table doesn't exist, create it
        CREATE TABLE public.user_allies (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            ally_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'blocked', 'declined')),
            connection_type VARCHAR(20) DEFAULT 'friend' CHECK (connection_type IN ('friend', 'colleague', 'collaborator', 'mentor', 'mentee')),
            requested_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            accepted_at TIMESTAMP WITH TIME ZONE,
            declined_at TIMESTAMP WITH TIME ZONE,
            blocked_at TIMESTAMP WITH TIME ZONE,
            created_by UUID NOT NULL REFERENCES auth.users(id), -- Who initiated the connection
            request_message TEXT,
            notes TEXT, -- Private notes about the connection
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

            -- Ensure users can't connect to themselves and prevent duplicate connections
            CONSTRAINT user_allies_no_self_connection CHECK (user_id != ally_id),
            CONSTRAINT user_allies_unique_connection UNIQUE (user_id, ally_id)
        );
    END IF;
END $$;

-- Create or update friend_requests table (enhanced version of existing concept)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'friend_requests') THEN
        -- Table exists, add missing columns if they don't exist

        -- Add request_type column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'friend_requests' AND column_name = 'request_type') THEN
            ALTER TABLE public.friend_requests ADD COLUMN request_type VARCHAR(20) DEFAULT 'friend' CHECK (request_type IN ('friend', 'colleague', 'collaborator'));
        END IF;

        -- Add expires_at column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'friend_requests' AND column_name = 'expires_at') THEN
            ALTER TABLE public.friend_requests ADD COLUMN expires_at TIMESTAMP WITH TIME ZONE DEFAULT (now() + INTERVAL '30 days');
        END IF;

        -- Add sent_at column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'friend_requests' AND column_name = 'sent_at') THEN
            ALTER TABLE public.friend_requests ADD COLUMN sent_at TIMESTAMP WITH TIME ZONE DEFAULT now();
        END IF;

        -- Add responded_at column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'friend_requests' AND column_name = 'responded_at') THEN
            ALTER TABLE public.friend_requests ADD COLUMN responded_at TIMESTAMP WITH TIME ZONE;
        END IF;

    ELSE
        -- Table doesn't exist, create it
        CREATE TABLE public.friend_requests (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            recipient_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            recipient_email VARCHAR(255), -- For inviting users not yet on platform
            message TEXT,
            status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired', 'cancelled')),
            request_type VARCHAR(20) DEFAULT 'friend' CHECK (request_type IN ('friend', 'colleague', 'collaborator')),
            expires_at TIMESTAMP WITH TIME ZONE DEFAULT (now() + INTERVAL '30 days'),
            sent_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            responded_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

            -- Either recipient_id or recipient_email must be provided
            CONSTRAINT friend_requests_recipient_check CHECK (
                (recipient_id IS NOT NULL) OR (recipient_email IS NOT NULL)
            )
        );
    END IF;
END $$;

-- ============================================================================
-- PRIVACY & DISCOVERY CONTROLS
-- ============================================================================

-- Create user_privacy_settings table
CREATE TABLE IF NOT EXISTS public.user_privacy_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    
    -- Discovery settings
    discoverable_by_email BOOLEAN DEFAULT true,
    discoverable_by_name BOOLEAN DEFAULT true,
    discoverable_by_skills BOOLEAN DEFAULT true,
    show_in_recommendations BOOLEAN DEFAULT true,
    
    -- Connection settings
    allow_friend_requests BOOLEAN DEFAULT true,
    require_mutual_connections BOOLEAN DEFAULT false,
    auto_accept_from_allies BOOLEAN DEFAULT false,
    
    -- Profile visibility
    profile_visibility VARCHAR(20) DEFAULT 'public' CHECK (profile_visibility IN ('public', 'allies_only', 'private')),
    show_ally_count BOOLEAN DEFAULT true,
    show_project_count BOOLEAN DEFAULT true,
    show_skills BOOLEAN DEFAULT true,
    show_activity_status BOOLEAN DEFAULT true,
    
    -- Communication preferences
    email_on_friend_request BOOLEAN DEFAULT true,
    email_on_request_accepted BOOLEAN DEFAULT true,
    push_notifications BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ============================================================================
-- ALLY DISCOVERY & RECOMMENDATIONS
-- ============================================================================

-- Create ally_recommendations table for storing recommendation algorithms
CREATE TABLE IF NOT EXISTS public.ally_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    recommended_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    recommendation_type VARCHAR(30) DEFAULT 'mutual_connections' CHECK (
        recommendation_type IN ('mutual_connections', 'shared_skills', 'project_collaboration', 'similar_interests', 'location_based', 'activity_based')
    ),
    score DECIMAL(3,2) DEFAULT 0.0, -- Recommendation strength 0.0-1.0
    reason_data JSONB DEFAULT '{}', -- Structured data explaining the recommendation
    shown_at TIMESTAMP WITH TIME ZONE,
    dismissed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Prevent duplicate recommendations
    CONSTRAINT ally_recommendations_unique UNIQUE (user_id, recommended_user_id, recommendation_type)
);

-- ============================================================================
-- SOCIAL INTERACTIONS & ACTIVITY
-- ============================================================================

-- Create social_interactions table for tracking user interactions
CREATE TABLE IF NOT EXISTS public.social_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    target_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    interaction_type VARCHAR(30) NOT NULL CHECK (
        interaction_type IN ('profile_view', 'message_sent', 'project_collaboration', 'skill_endorsement', 'recommendation_click')
    ),
    interaction_data JSONB DEFAULT '{}', -- Additional context data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Prevent self-interactions
    CONSTRAINT social_interactions_no_self CHECK (user_id != target_user_id)
);

-- Create skill_endorsements table for peer skill validation
CREATE TABLE IF NOT EXISTS public.skill_endorsements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    endorser_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    endorsed_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    skill_id UUID REFERENCES public.skills(id) ON DELETE CASCADE,
    skill_name VARCHAR(100), -- For skills not in the skills table
    endorsement_level INTEGER DEFAULT 3 CHECK (endorsement_level BETWEEN 1 AND 5), -- 1=Basic, 5=Expert
    endorsement_message TEXT,
    project_context VARCHAR(255), -- Which project this endorsement is based on
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Prevent self-endorsements and duplicate endorsements
    CONSTRAINT skill_endorsements_no_self CHECK (endorser_id != endorsed_user_id),
    CONSTRAINT skill_endorsements_unique UNIQUE (endorser_id, endorsed_user_id, skill_id, skill_name)
);

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- User allies indexes
CREATE INDEX IF NOT EXISTS idx_user_allies_user_id ON public.user_allies(user_id);
CREATE INDEX IF NOT EXISTS idx_user_allies_ally_id ON public.user_allies(ally_id);
CREATE INDEX IF NOT EXISTS idx_user_allies_status ON public.user_allies(status);
CREATE INDEX IF NOT EXISTS idx_user_allies_connection_type ON public.user_allies(connection_type);
CREATE INDEX IF NOT EXISTS idx_user_allies_created_by ON public.user_allies(created_by);

-- Friend requests indexes
CREATE INDEX IF NOT EXISTS idx_friend_requests_sender ON public.friend_requests(sender_id);
CREATE INDEX IF NOT EXISTS idx_friend_requests_recipient ON public.friend_requests(recipient_id);
CREATE INDEX IF NOT EXISTS idx_friend_requests_email ON public.friend_requests(recipient_email);
CREATE INDEX IF NOT EXISTS idx_friend_requests_status ON public.friend_requests(status);
CREATE INDEX IF NOT EXISTS idx_friend_requests_expires ON public.friend_requests(expires_at);

-- Privacy settings indexes
CREATE INDEX IF NOT EXISTS idx_privacy_settings_user ON public.user_privacy_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_privacy_settings_discoverable ON public.user_privacy_settings(discoverable_by_email, discoverable_by_name);

-- Recommendations indexes
CREATE INDEX IF NOT EXISTS idx_ally_recommendations_user ON public.ally_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_ally_recommendations_recommended ON public.ally_recommendations(recommended_user_id);
CREATE INDEX IF NOT EXISTS idx_ally_recommendations_type ON public.ally_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_ally_recommendations_score ON public.ally_recommendations(score DESC);

-- Social interactions indexes
CREATE INDEX IF NOT EXISTS idx_social_interactions_user ON public.social_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_social_interactions_target ON public.social_interactions(target_user_id);
CREATE INDEX IF NOT EXISTS idx_social_interactions_type ON public.social_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_social_interactions_created ON public.social_interactions(created_at DESC);

-- Skill endorsements indexes
CREATE INDEX IF NOT EXISTS idx_skill_endorsements_endorser ON public.skill_endorsements(endorser_id);
CREATE INDEX IF NOT EXISTS idx_skill_endorsements_endorsed ON public.skill_endorsements(endorsed_user_id);
CREATE INDEX IF NOT EXISTS idx_skill_endorsements_skill ON public.skill_endorsements(skill_id);

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE public.user_allies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.friend_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_privacy_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ally_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.skill_endorsements ENABLE ROW LEVEL SECURITY;

-- User allies policies
CREATE POLICY "Users can view their own connections" ON public.user_allies
    FOR SELECT USING (user_id = auth.uid() OR ally_id = auth.uid());

CREATE POLICY "Users can create connections" ON public.user_allies
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can update their connections" ON public.user_allies
    FOR UPDATE USING (user_id = auth.uid() OR ally_id = auth.uid());

-- Friend requests policies
CREATE POLICY "Users can view requests involving them" ON public.friend_requests
    FOR SELECT USING (
        sender_id = auth.uid() OR 
        recipient_id = auth.uid() OR 
        recipient_email = (SELECT email FROM auth.users WHERE id = auth.uid())
    );

CREATE POLICY "Users can send friend requests" ON public.friend_requests
    FOR INSERT WITH CHECK (sender_id = auth.uid());

CREATE POLICY "Users can update requests they're involved in" ON public.friend_requests
    FOR UPDATE USING (sender_id = auth.uid() OR recipient_id = auth.uid());

-- Privacy settings policies
CREATE POLICY "Users can manage their own privacy settings" ON public.user_privacy_settings
    FOR ALL USING (user_id = auth.uid());

-- Recommendations policies
CREATE POLICY "Users can view their own recommendations" ON public.ally_recommendations
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can manage recommendations" ON public.ally_recommendations
    FOR ALL USING (true); -- System-managed table

-- Social interactions policies
CREATE POLICY "Users can view interactions involving them" ON public.social_interactions
    FOR SELECT USING (user_id = auth.uid() OR target_user_id = auth.uid());

CREATE POLICY "Users can create interactions" ON public.social_interactions
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Skill endorsements policies
CREATE POLICY "Users can view endorsements involving them" ON public.skill_endorsements
    FOR SELECT USING (endorser_id = auth.uid() OR endorsed_user_id = auth.uid());

CREATE POLICY "Users can create endorsements" ON public.skill_endorsements
    FOR INSERT WITH CHECK (endorser_id = auth.uid());

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to get mutual connections between two users
CREATE OR REPLACE FUNCTION get_mutual_allies(user1_id UUID, user2_id UUID)
RETURNS TABLE(mutual_ally_id UUID, ally_name TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT 
        CASE 
            WHEN ua1.ally_id = ua2.ally_id THEN ua1.ally_id
            WHEN ua1.ally_id = ua2.user_id THEN ua1.ally_id
            WHEN ua1.user_id = ua2.ally_id THEN ua1.user_id
            ELSE ua1.user_id
        END as mutual_ally_id,
        u.display_name as ally_name
    FROM user_allies ua1
    JOIN user_allies ua2 ON (
        (ua1.ally_id = ua2.ally_id AND ua1.user_id = user1_id AND ua2.user_id = user2_id) OR
        (ua1.ally_id = ua2.user_id AND ua1.user_id = user1_id AND ua2.ally_id = user2_id) OR
        (ua1.user_id = ua2.ally_id AND ua1.ally_id = user1_id AND ua2.user_id = user2_id)
    )
    JOIN auth.users u ON u.id = CASE 
        WHEN ua1.ally_id = ua2.ally_id THEN ua1.ally_id
        WHEN ua1.ally_id = ua2.user_id THEN ua1.ally_id
        WHEN ua1.user_id = ua2.ally_id THEN ua1.user_id
        ELSE ua1.user_id
    END
    WHERE ua1.status = 'accepted' AND ua2.status = 'accepted';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if two users are allies
CREATE OR REPLACE FUNCTION are_users_allies(user1_id UUID, user2_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_allies 
        WHERE ((user_id = user1_id AND ally_id = user2_id) OR 
               (user_id = user2_id AND ally_id = user1_id))
        AND status = 'accepted'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- DEFAULT DATA & TRIGGERS
-- ============================================================================

-- Create default privacy settings for existing users
INSERT INTO public.user_privacy_settings (user_id)
SELECT id FROM auth.users 
WHERE id NOT IN (SELECT user_id FROM public.user_privacy_settings);

-- Trigger to create default privacy settings for new users
CREATE OR REPLACE FUNCTION create_default_privacy_settings()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_privacy_settings (user_id)
    VALUES (NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER trigger_create_privacy_settings
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION create_default_privacy_settings();

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON public.user_allies TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.friend_requests TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_privacy_settings TO authenticated;
GRANT SELECT ON public.ally_recommendations TO authenticated;
GRANT SELECT, INSERT ON public.social_interactions TO authenticated;
GRANT SELECT, INSERT ON public.skill_endorsements TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE public.user_allies IS 'Manages friend/ally connections between users with status tracking';
COMMENT ON TABLE public.friend_requests IS 'Handles friend request workflow including email invitations';
COMMENT ON TABLE public.user_privacy_settings IS 'User privacy controls for discovery and profile visibility';
COMMENT ON TABLE public.ally_recommendations IS 'Algorithm-generated user recommendations for networking';
COMMENT ON TABLE public.social_interactions IS 'Tracks user interactions for analytics and recommendations';
COMMENT ON TABLE public.skill_endorsements IS 'Peer-to-peer skill endorsements and validations';

COMMENT ON FUNCTION get_mutual_allies(UUID, UUID) IS 'Returns mutual connections between two users';
COMMENT ON FUNCTION are_users_allies(UUID, UUID) IS 'Checks if two users are connected as allies';

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify all tables were created
SELECT 'Ally system tables created' as status,
       COUNT(*) as table_count
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_allies', 'friend_requests', 'user_privacy_settings', 'ally_recommendations', 'social_interactions', 'skill_endorsements');

-- Verify indexes were created
SELECT 'Ally system indexes created' as status,
       COUNT(*) as index_count
FROM pg_indexes 
WHERE schemaname = 'public' 
AND indexname LIKE 'idx_%allies%' OR indexname LIKE 'idx_%friend%' OR indexname LIKE 'idx_%privacy%';

-- Verify functions were created
SELECT 'Ally system functions created' as status,
       COUNT(*) as function_count
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
AND p.proname IN ('get_mutual_allies', 'are_users_allies');
