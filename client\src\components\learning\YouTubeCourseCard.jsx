import React, { useState, useContext } from 'react';
import { Card, CardBody, Button, Chip, Progress, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure, Avatar, Badge } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import { Play, ExternalLink, Share2, BookOpen, Clock, Users, Star, CheckCircle, AlertCircle } from 'lucide-react';
import YouTubePlayer from './YouTubePlayer';
import ShareModal from './ShareModal';

/**
 * Enhanced YouTube Course Card Component
 *
 * Production-ready learning tile with thumbnails, vetting info, duration,
 * training track indicators, and comprehensive metadata display.
 * Features:
 * - Video thumbnail with hover effects and overlays
 * - Vetting status indicators and training track badges
 * - Progress tracking visualization
 * - Modal video player integration
 * - Sharing functionality
 * - Responsive design with enhanced UX
 * - Real database-driven content
 */
const YouTubeCourseCard = ({
  video,
  progress = 0,
  isEnrolled = false,
  onEnroll,
  onProgress,
  onComplete,
  onShare,
  showEnrollButton = true,
  showVettingInfo = true,
  trainingTrack = null,
  className = ''
}) => {
  const { currentUser } = useContext(UserContext);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isShareOpen, onOpen: onShareOpen, onClose: onShareClose } = useDisclosure();
  const [isLoading, setIsLoading] = useState(false);

  const handleEnroll = async () => {
    if (!currentUser) {
      toast.error('Please log in to enroll in courses');
      return;
    }

    if (onEnroll) {
      setIsLoading(true);
      try {
        await onEnroll(video);
        toast.success(`Enrolled in "${video.title}"`);
      } catch (error) {
        console.error('Error enrolling in video:', error);
        toast.error('Failed to enroll in course');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleWatchVideo = () => {
    onOpen();
  };

  // Handle sharing
  const handleShare = () => {
    if (onShare) {
      onShare(video);
    } else {
      onShareOpen();
    }
  };

  // Get video thumbnail with comprehensive fallback
  const getVideoThumbnail = () => {
    if (video.thumbnail_url) return video.thumbnail_url;
    if (video.thumbnail) return video.thumbnail;
    if (video.id || video.external_id) {
      const videoId = video.id || video.external_id;
      return `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;
    }
    // Fallback to a default educational video thumbnail
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMjggMTEyTDE5MiA3NkwxMjggNDBWMTEyWiIgZmlsbD0iIzMzMzMzMyIvPgo8L3N2Zz4K';
  };

  // Get video URL
  const getVideoUrl = () => {
    if (video.course_url) return video.course_url;
    if (video.url) return video.url;
    if (video.id || video.external_id) {
      const videoId = video.id || video.external_id;
      return `https://www.youtube.com/watch?v=${videoId}`;
    }
    return '#';
  };

  const formatDuration = (minutes) => {
    if (!minutes) return 'Unknown';
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const getProgressColor = () => {
    if (progress >= 100) return 'success';
    if (progress >= 50) return 'warning';
    return 'primary';
  };

  // Get difficulty color
  const getDifficultyColor = (level) => {
    switch (level?.toLowerCase()) {
      case 'beginner': case 'easy': return 'success';
      case 'intermediate': case 'medium': return 'warning';
      case 'advanced': case 'hard': return 'danger';
      default: return 'default';
    }
  };

  // Get vetting status info
  const getVettingStatus = () => {
    if (video.is_vetted) {
      return {
        status: 'vetted',
        icon: CheckCircle,
        color: 'success',
        text: 'Vetted Content'
      };
    } else if (video.metadata?.marked_for_vetting) {
      return {
        status: 'pending',
        icon: AlertCircle,
        color: 'warning',
        text: 'Pending Review'
      };
    }
    return null;
  };

  // Get training track info
  const getTrainingTrackInfo = () => {
    if (trainingTrack) {
      return {
        name: trainingTrack.name,
        color: trainingTrack.color || 'primary',
        icon: trainingTrack.icon || BookOpen
      };
    }

    // Infer from video categories or skills
    if (video.categories?.length > 0) {
      return {
        name: video.categories[0],
        color: 'primary',
        icon: BookOpen
      };
    }

    return null;
  };

  const getStatusChip = () => {
    if (progress >= 100) {
      return (
        <Chip color="success" size="sm" variant="flat" startContent={<CheckCircle className="w-3 h-3" />}>
          ✓ Completed
        </Chip>
      );
    }
    if (progress > 0) {
      return (
        <Chip color="warning" size="sm" variant="flat">
          In Progress
        </Chip>
      );
    }
    if (isEnrolled) {
      return (
        <Chip color="primary" size="sm" variant="flat">
          Enrolled
        </Chip>
      );
    }
    return null;
  };

  const vettingStatus = getVettingStatus();
  const trackInfo = getTrainingTrackInfo();

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={`h-full ${className}`}
      >
        <Card className="youtube-course-card h-full hover:shadow-lg transition-all duration-200 group">
          <CardBody className="p-0">
            {/* Video Thumbnail with Enhanced Overlays */}
            <div className="relative overflow-hidden rounded-t-lg">
              <img
                src={getVideoThumbnail()}
                alt={video.title}
                className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
                loading="lazy"
                onError={(e) => {
                  e.target.src = '/placeholder-video.jpg';
                }}
              />

              {/* Play Overlay */}
              <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                <div className="bg-white/90 rounded-full p-3">
                  <Play className="w-8 h-8 text-primary" fill="currentColor" />
                </div>
              </div>

              {/* Duration Badge */}
              <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded backdrop-blur-sm">
                <Clock className="w-3 h-3 inline mr-1" />
                {formatDuration(video.duration_minutes || video.duration)}
              </div>

              {/* Enrollment Status */}
              {isEnrolled && (
                <div className="absolute top-2 left-2">
                  <Badge content="" color="primary" size="sm" placement="top-right">
                    <Chip size="sm" color="primary" variant="solid" startContent={<CheckCircle className="w-3 h-3" />}>
                      Enrolled
                    </Chip>
                  </Badge>
                </div>
              )}

              {/* Vetting Status */}
              {showVettingInfo && vettingStatus && (
                <div className="absolute top-2 right-2">
                  <Chip
                    size="sm"
                    color={vettingStatus.color}
                    variant="flat"
                    startContent={<vettingStatus.icon className="w-3 h-3" />}
                  >
                    {vettingStatus.text}
                  </Chip>
                </div>
              )}

              {/* Provider Badge */}
              {!isEnrolled && (
                <div className="absolute top-2 left-2">
                  <Chip color="danger" size="sm" variant="solid">
                    YouTube
                  </Chip>
                </div>
              )}
            </div>

            {/* Video Information */}
            <div className="p-4 space-y-3">
              {/* Title and Channel */}
              <div>
                <h3 className="font-semibold text-sm line-clamp-2 leading-tight group-hover:text-primary transition-colors">
                  {video.title || 'Educational Video'}
                </h3>

                {(video.channelTitle || video.instructor_name) && (
                  <div className="flex items-center gap-2 mt-2">
                    <Avatar
                      size="sm"
                      name={video.channelTitle || video.instructor_name}
                      className="w-5 h-5"
                    />
                    <span className="text-xs text-default-600 truncate">
                      {video.channelTitle || video.instructor_name}
                    </span>
                  </div>
                )}
              </div>

              {/* Training track indicator */}
              {trackInfo && (
                <div className="flex items-center gap-2">
                  <trackInfo.icon className="w-4 h-4 text-default-600" />
                  <Chip size="sm" variant="flat" color={trackInfo.color}>
                    {trackInfo.name}
                  </Chip>
                </div>
              )}

              {/* Skills and difficulty */}
              <div className="flex flex-wrap gap-1">
                {(video.skills || []).slice(0, 2).map((skill, index) => (
                  <Chip key={index} size="sm" variant="flat" color="primary">
                    {skill}
                  </Chip>
                ))}
                {video.difficulty_level && (
                  <Chip
                    size="sm"
                    variant="flat"
                    color={getDifficultyColor(video.difficulty_level)}
                  >
                    {video.difficulty_level}
                  </Chip>
                )}
              </div>

              {/* Stats row */}
              <div className="flex items-center gap-4 text-xs text-default-600">
                {video.total_enrollments && (
                  <div className="flex items-center gap-1">
                    <Users className="w-3 h-3" />
                    <span>{video.total_enrollments}</span>
                  </div>
                )}
                {video.rating && (
                  <div className="flex items-center gap-1">
                    <Star className="w-3 h-3 fill-current text-warning" />
                    <span>{video.rating.toFixed(1)}</span>
                  </div>
                )}
                {video.publishedAt && (
                  <span className="text-xs text-default-500">
                    {new Date(video.publishedAt).toLocaleDateString()}
                  </span>
                )}
              </div>

              {/* Progress Bar (if enrolled) */}
              {isEnrolled && (
                <div className="space-y-1">
                  <div className="flex justify-between text-xs text-default-600">
                    <span>Progress</span>
                    <span>{Math.round(progress)}%</span>
                  </div>
                  <Progress
                    value={progress}
                    color="primary"
                    size="sm"
                    className="w-full"
                  />
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2">
                {!isEnrolled && showEnrollButton ? (
                  <Button
                    color="primary"
                    size="sm"
                    onPress={handleEnroll}
                    isLoading={isLoading}
                    className="flex-1"
                    startContent={<Play className="w-3 h-3" />}
                  >
                    Enroll
                  </Button>
                ) : (
                  <Button
                    color="primary"
                    variant="flat"
                    size="sm"
                    onPress={handleWatchVideo}
                    className="flex-1"
                    startContent={<Play className="w-3 h-3" />}
                  >
                    {progress > 0 ? 'Continue' : 'Start'}
                  </Button>
                )}

                <Button
                  color="default"
                  variant="light"
                  size="sm"
                  onPress={handleShare}
                  isIconOnly
                >
                  <Share2 className="w-3 h-3" />
                </Button>

                <Button
                  color="default"
                  variant="light"
                  size="sm"
                  as="a"
                  href={getVideoUrl()}
                  target="_blank"
                  rel="noopener noreferrer"
                  isIconOnly
                >
                  <ExternalLink className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Enhanced Video Player Modal */}
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        size="4xl"
        scrollBehavior="inside"
        classNames={{
          base: "youtube-player-modal",
          body: "p-0",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1 px-6 py-4">
            <h2 className="text-xl font-bold line-clamp-2">{video.title}</h2>
            <div className="flex items-center gap-2 text-sm text-default-600">
              <Chip color="danger" size="sm" variant="flat">
                YouTube
              </Chip>
              <span>{video.channelTitle || video.instructor_name}</span>
              {(video.duration_minutes || video.duration) && (
                <span>• {formatDuration(video.duration_minutes || video.duration)}</span>
              )}
              {vettingStatus && (
                <Chip
                  size="sm"
                  color={vettingStatus.color}
                  variant="flat"
                  startContent={<vettingStatus.icon className="w-3 h-3" />}
                >
                  {vettingStatus.text}
                </Chip>
              )}
            </div>
          </ModalHeader>

          <ModalBody className="px-0">
            <div className="space-y-4 px-6">
              {/* Embedded video */}
              <div className="aspect-video">
                <iframe
                  src={`https://www.youtube.com/embed/${video.id || video.external_id}?enablejsapi=1`}
                  title={video.title}
                  className="w-full h-full rounded-lg"
                  allowFullScreen
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                />
              </div>

              {/* Video description */}
              {video.description && (
                <div>
                  <h4 className="font-semibold mb-2">Description</h4>
                  <p className="text-sm text-default-700 whitespace-pre-wrap line-clamp-4">
                    {video.description}
                  </p>
                </div>
              )}

              {/* Skills and metadata */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {video.skills && video.skills.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-2">Skills Covered</h4>
                    <div className="flex flex-wrap gap-1">
                      {video.skills.map((skill, index) => (
                        <Chip key={index} size="sm" variant="flat" color="primary">
                          {skill}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}

                <div>
                  <h4 className="font-semibold mb-2">Details</h4>
                  <div className="space-y-1 text-sm text-default-600">
                    <div>Duration: {formatDuration(video.duration_minutes || video.duration)}</div>
                    {video.difficulty_level && (
                      <div>Difficulty: {video.difficulty_level}</div>
                    )}
                    {video.publishedAt && (
                      <div>Published: {new Date(video.publishedAt).toLocaleDateString()}</div>
                    )}
                    {video.total_enrollments && (
                      <div>Enrollments: {video.total_enrollments}</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </ModalBody>

          <ModalFooter className="px-6 py-4">
            <Button
              color="danger"
              variant="flat"
              onPress={onClose}
            >
              Close
            </Button>
            <Button
              color="default"
              variant="flat"
              onPress={handleShare}
              startContent={<Share2 className="w-4 h-4" />}
            >
              Share
            </Button>
            <Button
              color="primary"
              variant="flat"
              as="a"
              href={getVideoUrl()}
              target="_blank"
              rel="noopener noreferrer"
              startContent={<ExternalLink className="w-4 h-4" />}
            >
              Open in YouTube
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Share Modal */}
      <ShareModal
        isOpen={isShareOpen}
        onClose={onShareClose}
        content={video}
        contentType="video"
        userProgress={progress}
      />
    </>
  );
};

export default YouTubeCourseCard;
