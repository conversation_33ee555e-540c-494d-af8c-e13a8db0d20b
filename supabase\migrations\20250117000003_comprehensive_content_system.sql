-- Comprehensive Content Management System Migration
-- Supports multiple content types: articles, tutorials, guides, embedded content, external imports

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Content type enum
CREATE TYPE content_type AS ENUM (
  'article',
  'tutorial', 
  'guide',
  'video',
  'interactive',
  'embedded',
  'external_import',
  'royaltea_guide'
);

-- Content status enum
CREATE TYPE content_status AS ENUM (
  'draft',
  'pending_review',
  'published',
  'archived',
  'rejected'
);

-- Content difficulty enum
CREATE TYPE content_difficulty AS ENUM (
  'beginner',
  'intermediate', 
  'advanced',
  'expert'
);

-- Main learning content table
CREATE TABLE IF NOT EXISTS public.learning_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Basic metadata
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    content_type content_type NOT NULL DEFAULT 'article',
    status content_status NOT NULL DEFAULT 'draft',
    
    -- Content body (rich text/markdown)
    content_body TEXT, -- Main content in markdown/HTML
    content_summary TEXT, -- Brief summary/excerpt
    table_of_contents JSONB DEFAULT '[]'::jsonb, -- Auto-generated TOC
    
    -- Media and assets
    featured_image_url TEXT,
    thumbnail_url TEXT,
    media_assets JSONB DEFAULT '[]'::jsonb, -- Images, GIFs, videos, etc.
    
    -- Learning metadata
    difficulty_level content_difficulty DEFAULT 'beginner',
    estimated_read_time_minutes INTEGER,
    estimated_completion_time_minutes INTEGER,
    prerequisites TEXT[] DEFAULT '{}',
    learning_objectives TEXT[] DEFAULT '{}',
    skills_covered TEXT[] DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    
    -- Vetting integration
    vetting_level INTEGER CHECK (vetting_level >= 0 AND vetting_level <= 5),
    vetting_approved BOOLEAN DEFAULT FALSE,
    vetting_skills TEXT[] DEFAULT '{}',
    
    -- External content integration
    external_source_url TEXT,
    external_source_name TEXT,
    external_author_name TEXT,
    external_author_url TEXT,
    import_method VARCHAR(50), -- 'api', 'manual', 'scrape', 'embed'
    original_publish_date TIMESTAMPTZ,
    
    -- Authoring
    author_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    author_name TEXT,
    author_bio TEXT,
    author_avatar_url TEXT,
    
    -- Engagement metrics
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    bookmark_count INTEGER DEFAULT 0,
    completion_count INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,
    
    -- SEO and discoverability
    meta_title TEXT,
    meta_description TEXT,
    meta_keywords TEXT[] DEFAULT '{}',
    featured BOOLEAN DEFAULT FALSE,
    trending BOOLEAN DEFAULT FALSE,
    
    -- Organization
    category_id UUID,
    series_id UUID,
    parent_content_id UUID REFERENCES public.learning_content(id), -- For nested content
    sort_order INTEGER DEFAULT 0,
    
    -- Timestamps
    published_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Content categories table
CREATE TABLE IF NOT EXISTS public.content_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50), -- Icon class or emoji
    color VARCHAR(7), -- Hex color code
    parent_category_id UUID REFERENCES public.content_categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Content series table (for multi-part content)
CREATE TABLE IF NOT EXISTS public.content_series (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) NOT NULL UNIQUE,
    description TEXT,
    thumbnail_url TEXT,
    total_parts INTEGER DEFAULT 0,
    estimated_total_time_minutes INTEGER,
    difficulty_level content_difficulty DEFAULT 'beginner',
    skills_covered TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Content interactions table (likes, bookmarks, ratings)
CREATE TABLE IF NOT EXISTS public.content_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content_id UUID NOT NULL REFERENCES public.learning_content(id) ON DELETE CASCADE,
    interaction_type VARCHAR(20) NOT NULL CHECK (interaction_type IN ('like', 'bookmark', 'rating', 'view', 'completion')),
    rating INTEGER CHECK (rating >= 1 AND rating <= 5), -- Only for rating interactions
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, content_id, interaction_type)
);

-- Content progress tracking
CREATE TABLE IF NOT EXISTS public.content_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content_id UUID NOT NULL REFERENCES public.learning_content(id) ON DELETE CASCADE,
    
    -- Progress tracking
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    current_section VARCHAR(100), -- Current section/chapter
    time_spent_minutes INTEGER DEFAULT 0,
    last_position TEXT, -- JSON string for complex position tracking
    
    -- Status
    status VARCHAR(20) DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'bookmarked')),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    last_accessed_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Notes and highlights
    user_notes TEXT,
    highlights JSONB DEFAULT '[]'::jsonb,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, content_id)
);

-- External content sources table
CREATE TABLE IF NOT EXISTS public.external_content_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    base_url TEXT NOT NULL,
    api_endpoint TEXT,
    api_key_required BOOLEAN DEFAULT FALSE,
    supported_content_types TEXT[] DEFAULT '{}',
    import_method VARCHAR(50) NOT NULL, -- 'api', 'rss', 'scrape', 'manual'
    rate_limit_per_hour INTEGER DEFAULT 100,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Configuration
    import_config JSONB DEFAULT '{}'::jsonb,
    last_import_at TIMESTAMPTZ,
    total_imports INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Content import jobs table
CREATE TABLE IF NOT EXISTS public.content_import_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_id UUID REFERENCES public.external_content_sources(id),
    external_url TEXT NOT NULL,
    content_id UUID REFERENCES public.learning_content(id),
    
    -- Job details
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    import_method VARCHAR(50) NOT NULL,
    requested_by UUID REFERENCES auth.users(id),
    
    -- Processing details
    raw_content JSONB,
    processed_content JSONB,
    error_message TEXT,
    processing_time_seconds INTEGER,
    
    -- Metadata
    external_metadata JSONB DEFAULT '{}'::jsonb,
    import_settings JSONB DEFAULT '{}'::jsonb,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_learning_content_status ON public.learning_content(status);
CREATE INDEX IF NOT EXISTS idx_learning_content_type ON public.learning_content(content_type);
CREATE INDEX IF NOT EXISTS idx_learning_content_vetting_level ON public.learning_content(vetting_level);
CREATE INDEX IF NOT EXISTS idx_learning_content_author ON public.learning_content(author_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_category ON public.learning_content(category_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_series ON public.learning_content(series_id);
CREATE INDEX IF NOT EXISTS idx_learning_content_published ON public.learning_content(published_at);
CREATE INDEX IF NOT EXISTS idx_learning_content_featured ON public.learning_content(featured) WHERE featured = TRUE;
CREATE INDEX IF NOT EXISTS idx_learning_content_skills ON public.learning_content USING GIN(skills_covered);
CREATE INDEX IF NOT EXISTS idx_learning_content_tags ON public.learning_content USING GIN(tags);

CREATE INDEX IF NOT EXISTS idx_content_interactions_user ON public.content_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_content_interactions_content ON public.content_interactions(content_id);
CREATE INDEX IF NOT EXISTS idx_content_interactions_type ON public.content_interactions(interaction_type);

CREATE INDEX IF NOT EXISTS idx_content_progress_user ON public.content_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_content_progress_content ON public.content_progress(content_id);
CREATE INDEX IF NOT EXISTS idx_content_progress_status ON public.content_progress(status);

CREATE INDEX IF NOT EXISTS idx_content_import_jobs_status ON public.content_import_jobs(status);
CREATE INDEX IF NOT EXISTS idx_content_import_jobs_source ON public.content_import_jobs(source_id);

-- Create functions for automatic updates
CREATE OR REPLACE FUNCTION update_content_metrics()
RETURNS TRIGGER AS $$
BEGIN
    -- Update view count, like count, etc. on learning_content
    IF TG_OP = 'INSERT' THEN
        IF NEW.interaction_type = 'view' THEN
            UPDATE public.learning_content 
            SET view_count = view_count + 1 
            WHERE id = NEW.content_id;
        ELSIF NEW.interaction_type = 'like' THEN
            UPDATE public.learning_content 
            SET like_count = like_count + 1 
            WHERE id = NEW.content_id;
        ELSIF NEW.interaction_type = 'bookmark' THEN
            UPDATE public.learning_content 
            SET bookmark_count = bookmark_count + 1 
            WHERE id = NEW.content_id;
        ELSIF NEW.interaction_type = 'completion' THEN
            UPDATE public.learning_content 
            SET completion_count = completion_count + 1 
            WHERE id = NEW.content_id;
        ELSIF NEW.interaction_type = 'rating' THEN
            -- Update average rating
            UPDATE public.learning_content 
            SET 
                rating_count = rating_count + 1,
                average_rating = (
                    SELECT AVG(rating::DECIMAL) 
                    FROM public.content_interactions 
                    WHERE content_id = NEW.content_id 
                    AND interaction_type = 'rating'
                    AND rating IS NOT NULL
                )
            WHERE id = NEW.content_id;
        END IF;
    ELSIF TG_OP = 'DELETE' THEN
        -- Handle deletions (decrease counts)
        IF OLD.interaction_type = 'like' THEN
            UPDATE public.learning_content 
            SET like_count = GREATEST(like_count - 1, 0) 
            WHERE id = OLD.content_id;
        ELSIF OLD.interaction_type = 'bookmark' THEN
            UPDATE public.learning_content 
            SET bookmark_count = GREATEST(bookmark_count - 1, 0) 
            WHERE id = OLD.content_id;
        ELSIF OLD.interaction_type = 'rating' THEN
            -- Recalculate average rating
            UPDATE public.learning_content 
            SET 
                rating_count = GREATEST(rating_count - 1, 0),
                average_rating = COALESCE((
                    SELECT AVG(rating::DECIMAL) 
                    FROM public.content_interactions 
                    WHERE content_id = OLD.content_id 
                    AND interaction_type = 'rating'
                    AND rating IS NOT NULL
                ), 0)
            WHERE id = OLD.content_id;
        END IF;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger for content metrics
DROP TRIGGER IF EXISTS trigger_update_content_metrics ON public.content_interactions;
CREATE TRIGGER trigger_update_content_metrics
    AFTER INSERT OR DELETE ON public.content_interactions
    FOR EACH ROW
    EXECUTE FUNCTION update_content_metrics();

-- Create function for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic timestamp updates
DROP TRIGGER IF EXISTS trigger_update_learning_content_updated_at ON public.learning_content;
CREATE TRIGGER trigger_update_learning_content_updated_at
    BEFORE UPDATE ON public.learning_content
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_content_progress_updated_at ON public.content_progress;
CREATE TRIGGER trigger_update_content_progress_updated_at
    BEFORE UPDATE ON public.content_progress
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default content categories
INSERT INTO public.content_categories (name, slug, description, icon, color) VALUES
('Royaltea Platform', 'royaltea-platform', 'Learn about Royaltea features, revenue sharing, and project management', '👑', '#6366f1'),
('Revenue Sharing', 'revenue-sharing', 'Understanding royalty models, tranche systems, and payment distribution', '💰', '#10b981'),
('Project Management', 'project-management', 'Managing projects, teams, and contributors effectively', '📊', '#f59e0b'),
('Web Development', 'web-development', 'Frontend, backend, and full-stack development tutorials', '💻', '#3b82f6'),
('Design & UX', 'design-ux', 'UI/UX design, visual design, and user experience principles', '🎨', '#ec4899'),
('Business & Strategy', 'business-strategy', 'Business development, strategy, and entrepreneurship', '📈', '#8b5cf6'),
('Tools & Workflows', 'tools-workflows', 'Development tools, workflows, and productivity tips', '🛠️', '#06b6d4'),
('Community & Collaboration', 'community-collaboration', 'Building communities, team collaboration, and networking', '🤝', '#84cc16')
ON CONFLICT (slug) DO NOTHING;

-- Insert external content sources
INSERT INTO public.external_content_sources (name, base_url, supported_content_types, import_method) VALUES
('Unreal Engine Learning', 'https://dev.epicgames.com', ARRAY['tutorial', 'guide', 'article'], 'api'),
('Medium', 'https://medium.com', ARRAY['article', 'tutorial'], 'api'),
('Dev.to', 'https://dev.to', ARRAY['article', 'tutorial'], 'api'),
('GitHub Docs', 'https://docs.github.com', ARRAY['guide', 'tutorial'], 'scrape'),
('Personal Blogs', 'custom', ARRAY['article', 'tutorial', 'guide'], 'manual')
ON CONFLICT (name) DO NOTHING;

-- Insert default content categories
INSERT INTO public.content_categories (name, slug, description, icon, color) VALUES
('Royaltea Platform', 'royaltea-platform', 'Learn about Royaltea features, revenue sharing, and project management', '👑', '#6366f1'),
('Revenue Sharing', 'revenue-sharing', 'Understanding royalty models, tranche systems, and payment distribution', '💰', '#10b981'),
('Project Management', 'project-management', 'Managing projects, teams, and contributors effectively', '📊', '#f59e0b'),
('Web Development', 'web-development', 'Frontend, backend, and full-stack development tutorials', '💻', '#3b82f6'),
('Design & UX', 'design-ux', 'UI/UX design, visual design, and user experience principles', '🎨', '#ec4899'),
('Business & Strategy', 'business-strategy', 'Business development, strategy, and entrepreneurship', '📈', '#8b5cf6'),
('Tools & Workflows', 'tools-workflows', 'Development tools, workflows, and productivity tips', '🛠️', '#06b6d4'),
('Community & Collaboration', 'community-collaboration', 'Building communities, team collaboration, and networking', '🤝', '#84cc16')
ON CONFLICT (slug) DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE public.learning_content IS 'Main table for all learning content including articles, tutorials, guides, and external imports';
COMMENT ON TABLE public.content_categories IS 'Categories for organizing learning content';
COMMENT ON TABLE public.content_series IS 'Multi-part content series and learning paths';
COMMENT ON TABLE public.content_interactions IS 'User interactions with content (likes, bookmarks, ratings, views)';
COMMENT ON TABLE public.content_progress IS 'User progress tracking for learning content';
COMMENT ON TABLE public.external_content_sources IS 'Configuration for external content sources';
COMMENT ON TABLE public.content_import_jobs IS 'Jobs for importing content from external sources';

DO $$
BEGIN
RAISE NOTICE 'Comprehensive Content Management System created successfully!';
RAISE NOTICE 'Tables created: learning_content, content_categories, content_series, content_interactions, content_progress, external_content_sources, content_import_jobs';
RAISE NOTICE 'Added triggers for automatic metrics updates and timestamp management';
RAISE NOTICE 'Inserted default categories and external content sources';
END $$;