/**
 * Enhanced visibility handler for the application
 * This utility helps manage application behavior when the page visibility changes
 */

// Track the current visibility state
let isVisible = true;

// Track pending operations
const pendingOperations = [];

// Track if we've initialized the visibility listener
let listenerInitialized = false;

// Track the visibility timeout
let visibilityTimeout = null;

// Track focus/blur events to prevent rapid toggling
let lastFocusTime = 0;
let lastBlurTime = 0;
let lastVisibilityChangeTime = 0;

// Minimum time between visibility state changes (ms)
const MIN_STATE_CHANGE_INTERVAL = 1000;

// Delay before considering the page truly hidden (ms)
const HIDDEN_DELAY = 300000; // 5 minutes - much longer to prevent losing progress

// Flag to track if we're in a visibility transition
let inVisibilityTransition = false;

// The grace period after a visibility change (in milliseconds)
const VISIBILITY_TRANSITION_GRACE_PERIOD = 1000;

// Track if we're in dev tools mode (frequent focus/blur)
let devToolsMode = false;
let devToolsDetectionCount = 0;
const DEV_TOOLS_THRESHOLD = 2; // Number of rapid toggles before considering dev tools mode

// Track last time we reset dev tools mode
let lastDevToolsResetTime = 0;
const DEV_TOOLS_RESET_INTERVAL = 30000; // 30 seconds without rapid events before resetting

/**
 * Initialize the visibility change listener
 */
const initVisibilityListener = () => {
  if (listenerInitialized) return;

  // Set initial visibility state
  isVisible = document.visibilityState === 'visible';

  // Add event listener for visibility changes
  document.addEventListener('visibilitychange', handleVisibilityChange);

  // Add event listener for focus/blur events as a backup
  window.addEventListener('focus', handleFocus);
  window.addEventListener('blur', handleBlur);

  listenerInitialized = true;

  console.log('Visibility handler initialized, current state:', isVisible ? 'visible' : 'hidden');
};

/**
 * Check if an event is happening too quickly after a previous event
 * @param {number} lastTime - Timestamp of the last event
 * @returns {boolean} - Whether this is a rapid event
 */
const isRapidEvent = (lastTime) => {
  const now = Date.now();
  return (now - lastTime) < MIN_STATE_CHANGE_INTERVAL;
};

/**
 * Handle visibility change events
 */
const handleVisibilityChange = () => {
  const now = Date.now();
  const isRapid = isRapidEvent(lastVisibilityChangeTime);
  lastVisibilityChangeTime = now;

  // If this is a rapid change and we're in dev tools mode, ignore it
  if (isRapid && devToolsMode) {
    return;
  }

  // Check for dev tools pattern
  if (isRapid) {
    devToolsDetectionCount++;
    if (devToolsDetectionCount >= DEV_TOOLS_THRESHOLD) {
      devToolsMode = true;
      console.log('Dev tools mode detected - stabilizing visibility handling');
    }
  } else {
    // Reset counter if events are not rapid
    devToolsDetectionCount = 0;
  }

  console.log('Visibility changed to:', document.visibilityState);

  // Set the visibility transition flag
  inVisibilityTransition = true;

  // Clear the transition flag after the grace period
  setTimeout(() => {
    inVisibilityTransition = false;
  }, VISIBILITY_TRANSITION_GRACE_PERIOD);

  if (document.visibilityState === 'visible') {
    // Clear any pending timeout
    if (visibilityTimeout) {
      clearTimeout(visibilityTimeout);
      visibilityTimeout = null;
    }

    // Set visible immediately
    isVisible = true;

    // Process any pending operations
    processPendingOperations();
  } else {
    // When page becomes hidden, we'll keep it visible for much longer
    // This prevents the app from becoming unresponsive during tab switches
    // and prevents losing progress when tabbing away briefly

    // Only start a timeout if we don't already have one
    if (!visibilityTimeout) {
      visibilityTimeout = setTimeout(() => {
        // Double-check that we're still hidden after the long timeout
        if (document.visibilityState === 'hidden') {
          // Even then, don't change visibility state unless absolutely necessary
          // This is a conservative approach to prevent losing progress
          console.log('Page hidden for extended period, but keeping state active');

          // We're intentionally NOT setting isVisible = false here
          // to prevent the loading spinner from appearing and losing progress
        }
      }, HIDDEN_DELAY);
    }
  }
};

/**
 * Handle window focus events
 */
const handleFocus = () => {
  const now = Date.now();
  const isRapid = isRapidEvent(lastFocusTime);
  lastFocusTime = now;

  // If this is a rapid focus event and we're in dev tools mode, ignore it
  if (isRapid && devToolsMode) {
    return;
  }

  // Check for dev tools pattern
  if (isRapid) {
    devToolsDetectionCount++;
    if (devToolsDetectionCount >= DEV_TOOLS_THRESHOLD) {
      devToolsMode = true;
      console.log('Dev tools mode detected - stabilizing visibility handling');
    }
  } else {
    // Reset counter if events are not rapid
    devToolsDetectionCount = 0;
  }

  if (import.meta.env?.DEV) {
    console.log('Window focused');
  }

  // Clear any pending timeout
  if (visibilityTimeout) {
    clearTimeout(visibilityTimeout);
    visibilityTimeout = null;
  }

  // Set visible immediately
  isVisible = true;

  // Process any pending operations
  processPendingOperations();
};

/**
 * Handle window blur events
 */
const handleBlur = () => {
  const now = Date.now();
  const isRapid = isRapidEvent(lastBlurTime);
  lastBlurTime = now;

  // If this is a rapid blur event and we're in dev tools mode, ignore it
  if (isRapid && devToolsMode) {
    return;
  }

  // Check for dev tools pattern
  if (isRapid) {
    devToolsDetectionCount++;
    if (devToolsDetectionCount >= DEV_TOOLS_THRESHOLD) {
      devToolsMode = true;
      console.log('Dev tools mode detected - stabilizing visibility handling');
    }
  } else {
    // Reset counter if events are not rapid
    devToolsDetectionCount = 0;
  }

  if (import.meta.env?.DEV) {
    console.log('Window blurred');
  }

  // Don't set hidden immediately, use a timeout
  // This prevents the app from becoming unresponsive during brief tab switches
  if (visibilityTimeout) {
    clearTimeout(visibilityTimeout);
  }

  // We're using a much longer timeout and not changing the visibility state
  // This prevents the loading spinner from appearing when tabbing away briefly
  visibilityTimeout = setTimeout(() => {
    // Only log that we've been away for a while, but don't change state
    if ((now - lastFocusTime) > MIN_STATE_CHANGE_INTERVAL) {
      console.log('Page blurred for extended period, but keeping state active');
      // Intentionally NOT setting isVisible = false to prevent losing progress
    }
  }, HIDDEN_DELAY);
};

/**
 * Process any pending operations
 */
const processPendingOperations = () => {
  if (pendingOperations.length === 0) return;

  console.log(`Processing ${pendingOperations.length} pending operations`);

  // Process each operation
  const operations = [...pendingOperations];
  pendingOperations.length = 0; // Clear the array

  operations.forEach(operation => {
    try {
      operation();
    } catch (error) {
      console.error('Error processing operation:', error);
    }
  });
};

/**
 * Queue an operation to be performed when the page becomes visible
 * @param {Function} operation - The operation to queue
 * @param {boolean} executeImmediatelyIfVisible - Whether to execute immediately if the page is visible
 */
export const queueOperation = (operation, executeImmediatelyIfVisible = true) => {
  // Initialize the visibility listener if not already done
  if (!listenerInitialized) {
    initVisibilityListener();
  }

  // If page is visible and we should execute immediately, do so
  if (isVisible && executeImmediatelyIfVisible) {
    operation();
  } else {
    // Otherwise, queue the operation
    pendingOperations.push(operation);
    console.log('Operation queued for when page becomes visible');
  }
};

/**
 * Check if the page is currently visible
 * @returns {boolean} - Whether the page is visible
 */
export const isPageVisible = () => {
  // Initialize the visibility listener if not already done
  if (!listenerInitialized) {
    initVisibilityListener();
  }

  return isVisible;
};

/**
 * Check if we're currently in a visibility transition
 * @returns {boolean} - Whether we're in a visibility transition
 */
export const isInVisibilityTransition = () => {
  return inVisibilityTransition;
};

/**
 * Reset dev tools detection
 */
export const resetDevToolsDetection = () => {
  devToolsMode = false;
  devToolsDetectionCount = 0;
  lastDevToolsResetTime = Date.now();
  console.log('Dev tools detection reset');
};

/**
 * Clean up the visibility handler
 * Call this when the component unmounts
 */
export const cleanupVisibilityHandler = () => {
  if (!listenerInitialized) return;

  // Remove event listeners
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  window.removeEventListener('focus', handleFocus);
  window.removeEventListener('blur', handleBlur);

  // Clear any pending timeout
  if (visibilityTimeout) {
    clearTimeout(visibilityTimeout);
    visibilityTimeout = null;
  }

  // Reset state
  listenerInitialized = false;
  isVisible = true;
  pendingOperations.length = 0;
  devToolsMode = false;
  devToolsDetectionCount = 0;

  console.log('Visibility handler cleaned up');
};

/**
 * Check if we're in dev tools mode
 * @returns {boolean} - Whether we're in dev tools mode
 */
export const isDevToolsMode = () => {
  const now = Date.now();

  // Auto-reset dev tools mode after a period of inactivity
  if (devToolsMode && (now - lastDevToolsResetTime > DEV_TOOLS_RESET_INTERVAL)) {
    // If it's been a while since the last rapid event, reset dev tools mode
    if ((now - Math.max(lastFocusTime, lastBlurTime, lastVisibilityChangeTime)) > DEV_TOOLS_RESET_INTERVAL) {
      console.log('Auto-resetting dev tools mode after period of inactivity');
      devToolsMode = false;
      devToolsDetectionCount = 0;
      lastDevToolsResetTime = now;
    }
  }

  return devToolsMode;
};

export default {
  queueOperation,
  isPageVisible,
  isDevToolsMode,
  resetDevToolsDetection,
  cleanupVisibilityHandler
};
