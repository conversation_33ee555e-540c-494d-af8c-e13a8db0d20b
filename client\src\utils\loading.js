/**
 * Consolidated Loading Utility
 * 
 * This utility combines the best features of focus-aware-loading, loading-monitor, and loading-tracker
 * to provide a comprehensive solution for managing loading states across the application.
 * 
 * Features:
 * - Track loading states and their changes
 * - Monitor page visibility to prevent stuck loading states
 * - Provide hooks for React components
 * - Debug loading state issues
 */

import { useState, useEffect } from 'react';

// ===== VISIBILITY TRACKING =====

// Track page visibility state
let isPageVisible = true;
let visibilityChangeHandlers = [];

// Initialize visibility tracking
export const initVisibilityTracking = () => {
  // Set initial state
  isPageVisible = document.visibilityState === 'visible';

  // Add event listener for visibility changes
  document.addEventListener('visibilitychange', handleVisibilityChange);

  // Add event listener for focus/blur
  window.addEventListener('focus', handleFocus);
  window.addEventListener('blur', handleBlur);

  console.log('[Loading] Visibility tracking initialized');
};

// Clean up visibility tracking
export const cleanupVisibilityTracking = () => {
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  window.removeEventListener('focus', handleFocus);
  window.removeEventListener('blur', handleBlur);
  visibilityChangeHandlers = [];
  console.log('[Loading] Visibility tracking cleaned up');
};

// Handle visibility change
const handleVisibilityChange = () => {
  const wasVisible = isPageVisible;
  isPageVisible = document.visibilityState === 'visible';

  console.log(`[Loading] Visibility changed: ${wasVisible} -> ${isPageVisible}`);

  // Track the time of the visibility change
  window.lastVisibilityChangeTime = Date.now();

  // If page becomes visible after being hidden, reset loading states
  if (isPageVisible && !wasVisible) {
    resetLoadingStates();
  }

  // Notify all handlers
  notifyVisibilityHandlers();
};

// Handle window focus
const handleFocus = () => {
  const wasVisible = isPageVisible;
  isPageVisible = true;

  if (import.meta.env?.DEV) {
    console.log(`[Loading] Window focus: ${wasVisible} -> ${isPageVisible}`);
  }

  // Track the time of the focus change
  window.lastVisibilityChangeTime = Date.now();

  // If page becomes visible after being hidden, reset loading states
  if (!wasVisible) {
    resetLoadingStates();
  }

  // Notify all handlers
  notifyVisibilityHandlers();
};

// Handle window blur
const handleBlur = () => {
  const wasVisible = isPageVisible;
  isPageVisible = false;

  if (import.meta.env?.DEV) {
    console.log(`[Loading] Window blur: ${wasVisible} -> ${isPageVisible}`);
  }

  // Track the time of the blur change
  window.lastVisibilityChangeTime = Date.now();

  // Notify all handlers
  notifyVisibilityHandlers();
};

// Notify all visibility change handlers
const notifyVisibilityHandlers = () => {
  visibilityChangeHandlers.forEach(handler => {
    try {
      handler(isPageVisible);
    } catch (error) {
      console.error('[Loading] Error in visibility handler:', error);
    }
  });
};

// Register a visibility change handler
export const registerVisibilityHandler = (handler) => {
  if (typeof handler === 'function' && !visibilityChangeHandlers.includes(handler)) {
    visibilityChangeHandlers.push(handler);
    return true;
  }
  return false;
};

// Unregister a visibility change handler
export const unregisterVisibilityHandler = (handler) => {
  const index = visibilityChangeHandlers.indexOf(handler);
  if (index !== -1) {
    visibilityChangeHandlers.splice(index, 1);
    return true;
  }
  return false;
};

// Get current page visibility state
export const getPageVisibility = () => {
  return isPageVisible;
};

// ===== LOADING STATE TRACKING =====

// Track all active loading operations
const activeLoadingOperations = new Map();

// Track loading history for debugging
const loadingHistory = [];
const MAX_HISTORY_LENGTH = 50;

// Track all loading state changes
const loadingStateChanges = [];

// Track current loading states
const activeLoadingStates = new Map();

/**
 * Register a new loading operation
 * @param {string} componentName - The name of the component that's loading
 * @param {string} operationName - The name of the specific operation
 * @param {string} reason - The reason for the loading state
 * @returns {string} - A unique ID for this loading operation
 */
export const startLoading = (componentName, operationName, reason = '') => {
  const timestamp = new Date();
  const id = `${componentName}-${operationName}-${timestamp.getTime()}`;

  activeLoadingOperations.set(id, {
    componentName,
    operationName,
    reason,
    startTime: timestamp,
    stack: new Error().stack
  });

  const activeCount = activeLoadingOperations.size;

  // Add to history
  addToHistory({
    type: 'start',
    id,
    componentName,
    operationName,
    reason,
    timestamp,
    activeCount
  });

  // Log the loading start
  console.log(`[Loading] START: ${componentName} - ${operationName}${reason ? ` (${reason})` : ''} [${activeCount} active]`);

  return id;
};

/**
 * End a loading operation
 * @param {string} id - The ID of the loading operation to end
 * @param {string} result - The result of the operation (success, error, etc.)
 */
export const endLoading = (id, result = 'success') => {
  if (!activeLoadingOperations.has(id)) {
    // Find operations with the same component and operation name
    // This handles cases where the same operation is started multiple times
    const [componentName, operationName] = id.split('-').slice(0, 2);

    // Find all active operations from the same component and with the same operation name
    const matchingOperations = Array.from(activeLoadingOperations.entries())
      .filter(([_, op]) =>
        op.componentName === componentName &&
        op.operationName === operationName
      );

    if (matchingOperations.length > 0) {
      // End the oldest matching operation instead
      const [oldestId, oldestOp] = matchingOperations.sort((a, b) =>
        a[1].startTime - b[1].startTime
      )[0];

      console.warn(`[Loading] Operation ID mismatch, ending similar operation: ${oldestId} instead of ${id}`);

      const duration = new Date() - oldestOp.startTime;

      // Remove from active operations
      activeLoadingOperations.delete(oldestId);

      const activeCount = activeLoadingOperations.size;

      // Add to history
      addToHistory({
        type: 'end',
        id: oldestId,
        componentName: oldestOp.componentName,
        operationName: oldestOp.operationName,
        result: result + ' (ID mismatch)',
        duration,
        timestamp: new Date(),
        activeCount
      });

      // Log the loading end
      console.log(`[Loading] END: ${oldestOp.componentName} - ${oldestOp.operationName} (${duration}ms, ${result} - ID mismatch) [${activeCount} active]`);
      return;
    }

    console.warn(`[Loading] Attempted to end unknown loading operation: ${id}`);
    return;
  }

  const operation = activeLoadingOperations.get(id);
  const duration = new Date() - operation.startTime;

  // Remove from active operations
  activeLoadingOperations.delete(id);

  const activeCount = activeLoadingOperations.size;

  // Add to history
  addToHistory({
    type: 'end',
    id,
    componentName: operation.componentName,
    operationName: operation.operationName,
    result,
    duration,
    timestamp: new Date(),
    activeCount
  });

  // Log the loading end
  console.log(`[Loading] END: ${operation.componentName} - ${operation.operationName} (${duration}ms, ${result}) [${activeCount} active]`);
};

/**
 * Track a loading state change
 * @param {string} component - The component name
 * @param {string} property - The loading property name
 * @param {boolean} value - The new loading state value
 */
export const trackLoadingState = (component, property, value) => {
  const timestamp = new Date();
  const id = `${component}:${property}`;
  
  // Get the stack trace
  let stack;
  try {
    throw new Error('Loading state change');
  } catch (e) {
    stack = e.stack;
  }
  
  // Create a record of the change
  const record = {
    timestamp,
    component,
    property,
    value,
    stack: stack.split('\n').slice(2, 7).join('\n') // Keep only relevant part of stack
  };
  
  // Add to history
  loadingStateChanges.unshift(record);
  
  // Trim history if needed
  if (loadingStateChanges.length > MAX_HISTORY_LENGTH) {
    loadingStateChanges.pop();
  }
  
  // Update active loading states
  if (value) {
    activeLoadingStates.set(id, record);
  } else {
    activeLoadingStates.delete(id);
  }
  
  // Log the change
  console.log(`[Loading] ${component} ${property} = ${value}`);
};

/**
 * Add an entry to the loading history
 * @param {Object} entry - The history entry to add
 */
const addToHistory = (entry) => {
  loadingHistory.unshift(entry);

  // Trim history if it gets too long
  if (loadingHistory.length > MAX_HISTORY_LENGTH) {
    loadingHistory.pop();
  }
};

/**
 * Reset all loading states
 */
export const resetLoadingStates = () => {
  // Don't reset loading states if we're in the middle of an auth operation
  if (window.isAuthOperationInProgress) {
    console.log('[Loading] Auth operation in progress, skipping loading state reset');
    return;
  }

  if (activeLoadingOperations.size > 0) {
    console.warn(`[Loading] Forcibly resetting ${activeLoadingOperations.size} active loading operations`);

    // Log all active operations before clearing
    activeLoadingOperations.forEach((operation, id) => {
      const duration = new Date() - operation.startTime;
      console.warn(`[Loading] Forced reset: ${operation.componentName} - ${operation.operationName} (${duration}ms)`);
    });

    // Clear all active operations
    activeLoadingOperations.clear();

    // Add to history
    addToHistory({
      type: 'reset',
      timestamp: new Date(),
      activeCount: 0
    });
  }

  // Find all components with loading states
  const loadingComponents = document.querySelectorAll('.loading-container, .loading-container-fullpage');

  // Remove loading containers
  loadingComponents.forEach(component => {
    try {
      // Skip auth-related loading components
      if (component.textContent &&
          (component.textContent.includes('AUTH ROUTE') ||
           component.textContent.includes('AUTH CONTEXT') ||
           component.textContent.includes('APP.JSX'))) {
        console.log('[Loading] Skipping auth-related loading component:', component.textContent);
        return;
      }

      if (component && component.parentNode) {
        component.parentNode.removeChild(component);
      }
    } catch (error) {
      console.error('[Loading] Error removing loading component:', error);
    }
  });

  // Dispatch a custom event that components can listen for
  window.dispatchEvent(new CustomEvent('resetLoadingStates'));
};

// ===== REACT HOOKS =====

/**
 * Hook to track page visibility
 * @returns {boolean} - Whether the page is currently visible
 */
export const usePageVisibility = () => {
  const [isVisible, setIsVisible] = useState(isPageVisible);

  useEffect(() => {
    const handler = (visible) => {
      setIsVisible(visible);
    };

    // Register handler
    registerVisibilityHandler(handler);

    // Set initial state
    setIsVisible(isPageVisible);

    // Cleanup
    return () => {
      unregisterVisibilityHandler(handler);
    };
  }, []);

  return isVisible;
};

/**
 * Hook for loading monitoring in React components
 * @param {string} componentName - The name of the component
 * @returns {Object} - Loading utility functions
 */
export const useLoadingMonitor = (componentName) => {
  return {
    startLoading: (operationName, reason) => startLoading(componentName, operationName, reason),
    endLoading,
    isLoading: () => activeLoadingOperations.size > 0,
    getActiveLoadingCount: () => activeLoadingOperations.size
  };
};

// ===== DEBUGGING UTILITIES =====

/**
 * Get all loading state changes
 * @returns {Array} - The loading state changes
 */
export const getLoadingStateChanges = () => {
  return [...loadingStateChanges];
};

/**
 * Get all active loading states
 * @returns {Array} - The active loading states
 */
export const getActiveLoadingStates = () => {
  return Array.from(activeLoadingStates.values());
};

/**
 * Get the loading history
 * @returns {Array} - The loading history
 */
export const getLoadingHistory = () => {
  return [...loadingHistory];
};

/**
 * Clear all loading state history
 */
export const clearLoadingStateHistory = () => {
  loadingStateChanges.length = 0;
  loadingHistory.length = 0;
};

/**
 * Get all active loading operations
 * @returns {Array} - Array of active loading operations
 */
export const getActiveLoadingOperations = () => {
  return Array.from(activeLoadingOperations.entries()).map(([id, operation]) => ({
    id,
    ...operation,
    duration: new Date() - operation.startTime
  }));
};

// Initialize visibility tracking when this module is imported
initVisibilityTracking();

export default {
  // Visibility tracking
  getPageVisibility,
  usePageVisibility,
  registerVisibilityHandler,
  unregisterVisibilityHandler,
  initVisibilityTracking,
  cleanupVisibilityTracking,
  
  // Loading operations
  startLoading,
  endLoading,
  trackLoadingState,
  resetLoadingStates,
  useLoadingMonitor,
  
  // Debugging
  getLoadingStateChanges,
  getActiveLoadingStates,
  getLoadingHistory,
  clearLoadingStateHistory,
  getActiveLoadingOperations
};
