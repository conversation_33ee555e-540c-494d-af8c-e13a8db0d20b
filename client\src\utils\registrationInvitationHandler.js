// Registration Invitation Handler
// Handles automatic invitation acceptance when users register

import { supabase } from './supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Check for and accept pending invitations when a user registers
 * @param {Object} user - The newly registered user object
 * @param {string} email - User's email address
 * @returns {Promise<Object>} - Summary of accepted invitations
 */
export const handleRegistrationInvitations = async (user, email) => {
  console.log('🔍 Checking for pending invitations for:', email);
  
  const acceptedInvitations = {
    projects: [],
    friends: [],
    teams: [],
    studios: [],
    total: 0
  };

  try {
    // Check for project invitations
    const projectInvitations = await acceptProjectInvitations(user, email);
    acceptedInvitations.projects = projectInvitations;
    acceptedInvitations.total += projectInvitations.length;

    // Check for friend requests
    const friendRequests = await acceptFriendRequests(user, email);
    acceptedInvitations.friends = friendRequests;
    acceptedInvitations.total += friendRequests.length;

    // Check for team invitations (if table exists)
    const teamInvitations = await acceptTeamInvitations(user, email);
    acceptedInvitations.teams = teamInvitations;
    acceptedInvitations.total += teamInvitations.length;

    // Check for studio invitations (if table exists)
    const studioInvitations = await acceptStudioInvitations(user, email);
    acceptedInvitations.studios = studioInvitations;
    acceptedInvitations.total += studioInvitations.length;

    // Show success message if invitations were accepted
    if (acceptedInvitations.total > 0) {
      toast.success(`Welcome! You've been automatically added to ${acceptedInvitations.total} invitation(s).`);
      console.log('✅ Accepted invitations:', acceptedInvitations);
    }

    return acceptedInvitations;

  } catch (error) {
    console.error('❌ Error handling registration invitations:', error);
    // Don't throw - registration should succeed even if invitation handling fails
    return acceptedInvitations;
  }
};

/**
 * Accept pending project invitations
 */
const acceptProjectInvitations = async (user, email) => {
  try {
    // Find pending project invitations for this email
    const { data: invitations, error: fetchError } = await supabase
      .from('project_contributors')
      .select(`
        id,
        project_id,
        email,
        display_name,
        role,
        permission_level,
        is_admin,
        projects:project_id (
          name,
          description
        )
      `)
      .eq('email', email)
      .eq('status', 'pending')
      .is('user_id', null);

    if (fetchError) {
      console.error('Error fetching project invitations:', fetchError);
      return [];
    }

    if (!invitations || invitations.length === 0) {
      return [];
    }

    console.log(`📧 Found ${invitations.length} pending project invitation(s)`);

    // Update invitations to link to the new user and set as active
    const { data: updatedInvitations, error: updateError } = await supabase
      .from('project_contributors')
      .update({
        user_id: user.id,
        status: 'active',
        joined_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .in('id', invitations.map(inv => inv.id))
      .select();

    if (updateError) {
      console.error('Error updating project invitations:', updateError);
      return [];
    }

    console.log(`✅ Accepted ${updatedInvitations.length} project invitation(s)`);
    return updatedInvitations;

  } catch (error) {
    console.error('Error in acceptProjectInvitations:', error);
    return [];
  }
};

/**
 * Accept pending friend requests
 */
const acceptFriendRequests = async (user, email) => {
  try {
    // Find pending friend requests for this email
    const { data: requests, error: fetchError } = await supabase
      .from('friend_requests')
      .select('*')
      .eq('recipient_email', email)
      .eq('status', 'pending')
      .is('recipient_id', null);

    if (fetchError) {
      console.error('Error fetching friend requests:', fetchError);
      return [];
    }

    if (!requests || requests.length === 0) {
      return [];
    }

    console.log(`👥 Found ${requests.length} pending friend request(s)`);

    // Update friend requests to link to the new user
    const { data: updatedRequests, error: updateError } = await supabase
      .from('friend_requests')
      .update({
        recipient_id: user.id,
        status: 'accepted',
        updated_at: new Date().toISOString()
      })
      .in('id', requests.map(req => req.id))
      .select();

    if (updateError) {
      console.error('Error updating friend requests:', updateError);
      return [];
    }

    // Create friend relationships for each accepted request
    const friendshipPromises = updatedRequests.map(async (request) => {
      try {
        // Create bidirectional friendship
        await supabase.from('friends').insert([
          {
            user_id: request.recipient_id,
            friend_id: request.sender_id,
            created_at: new Date().toISOString()
          },
          {
            user_id: request.sender_id,
            friend_id: request.recipient_id,
            created_at: new Date().toISOString()
          }
        ]);
      } catch (error) {
        console.error('Error creating friendship:', error);
      }
    });

    await Promise.allSettled(friendshipPromises);

    console.log(`✅ Accepted ${updatedRequests.length} friend request(s)`);
    return updatedRequests;

  } catch (error) {
    console.error('Error in acceptFriendRequests:', error);
    return [];
  }
};

/**
 * Accept pending team invitations
 */
const acceptTeamInvitations = async (user, email) => {
  try {
    // Check if team_invitations table exists
    const { data: invitations, error: fetchError } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('invited_email', email)
      .eq('status', 'pending')
      .is('invited_user_id', null);

    if (fetchError) {
      // Table might not exist, return empty array
      if (fetchError.code === '42P01') {
        return [];
      }
      console.error('Error fetching team invitations:', fetchError);
      return [];
    }

    if (!invitations || invitations.length === 0) {
      return [];
    }

    console.log(`🏢 Found ${invitations.length} pending team invitation(s)`);

    // Update invitations and add user to teams
    const acceptedInvitations = [];
    
    for (const invitation of invitations) {
      try {
        // Update invitation status
        await supabase
          .from('team_invitations')
          .update({
            invited_user_id: user.id,
            status: 'accepted',
            responded_at: new Date().toISOString()
          })
          .eq('id', invitation.id);

        // Add user to team
        await supabase
          .from('team_members')
          .insert({
            team_id: invitation.team_id,
            user_id: user.id,
            role: invitation.role || 'member',
            is_admin: invitation.role === 'admin',
            joined_at: new Date().toISOString()
          });

        acceptedInvitations.push(invitation);
      } catch (error) {
        console.error('Error accepting team invitation:', error);
      }
    }

    console.log(`✅ Accepted ${acceptedInvitations.length} team invitation(s)`);
    return acceptedInvitations;

  } catch (error) {
    console.error('Error in acceptTeamInvitations:', error);
    return [];
  }
};

/**
 * Accept pending studio invitations
 */
const acceptStudioInvitations = async (user, email) => {
  try {
    // Check if alliance_invitations table exists (studios were previously called alliances)
    const { data: invitations, error: fetchError } = await supabase
      .from('alliance_invitations')
      .select('*')
      .eq('email', email)
      .eq('status', 'pending');

    if (fetchError) {
      // Table might not exist, return empty array
      if (fetchError.code === '42P01') {
        return [];
      }
      console.error('Error fetching studio invitations:', fetchError);
      return [];
    }

    if (!invitations || invitations.length === 0) {
      return [];
    }

    console.log(`🏭 Found ${invitations.length} pending studio invitation(s)`);

    // Update invitations and add user to studios
    const acceptedInvitations = [];
    
    for (const invitation of invitations) {
      try {
        // Update invitation status
        await supabase
          .from('alliance_invitations')
          .update({
            status: 'accepted',
            accepted_at: new Date().toISOString()
          })
          .eq('id', invitation.id);

        // Add user to studio/alliance
        await supabase
          .from('team_members')
          .insert({
            team_id: invitation.alliance_id,
            user_id: user.id,
            role: invitation.role || 'member',
            is_admin: invitation.role === 'admin',
            joined_at: new Date().toISOString()
          });

        acceptedInvitations.push(invitation);
      } catch (error) {
        console.error('Error accepting studio invitation:', error);
      }
    }

    console.log(`✅ Accepted ${acceptedInvitations.length} studio invitation(s)`);
    return acceptedInvitations;

  } catch (error) {
    console.error('Error in acceptStudioInvitations:', error);
    return [];
  }
};
