// CRITICAL TEST: Add global marker before any imports (production mode)
if (import.meta.env.DEV) {
  console.log('🚨 GLOBAL TEST: JavaScript is executing!');
}
window.JAVASCRIPT_EXECUTING = true;

import React, { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON>h<PERSON>outer as Router } from "react-router-dom";
import { HeroUIProvider } from "@heroui/react";
import { UserProvider } from "./contexts/supabase-auth.context.jsx";
import ThemeProvider from "./contexts/theme.context.jsx";
import FeatureFlagProvider from "./contexts/feature-flags.context.jsx";
import App from "./App.jsx";
import ExtensionErrorBoundary from "./components/error/ExtensionErrorBoundary.jsx";

// Import CSS
import "./index.css";
import "./styles/tailwind.css";

// Import and initialize browser extension error handler
import { initBrowserExtensionHandler } from "./utils/browserExtensionHandler.js";

// Initialize browser extension error handler early
initBrowserExtensionHandler();

// CRITICAL DEBUG: Test if main.jsx is executing at all (production mode)
if (import.meta.env.DEV) {
  console.log('🚨 CRITICAL DEBUG: main.jsx file is executing!');
  console.log('🚨 CRITICAL DEBUG: React import:', React);
  console.log('🚨 CRITICAL DEBUG: createRoot import:', createRoot);
}
window.MAIN_JSX_EXECUTED = true;

// Simple test component
function TestApp() {
  return (
    <div>
      <h1>🎉 React is Working!</h1>
      <p>This is a minimal React app to test if the flat import issue is resolved.</p>
    </div>
  );
}

// Test if we can create a root at all
try {
  if (import.meta.env.DEV) {
    console.log('🔍 DEBUG: main.jsx executing - React should be working');
    console.log('🔍 DEBUG: Attempting to create React root...');
  }

  const root = createRoot(document.getElementById("root"));

  if (import.meta.env.DEV) {
    console.log('🔍 DEBUG: React root created successfully:', root);
    console.log('🔍 DEBUG: Attempting to render React app...');
  }

  root.render(
    <StrictMode>
      <ExtensionErrorBoundary>
        <Router>
          <UserProvider>
            <ThemeProvider>
              <HeroUIProvider>{/* Let HeroUI handle theme switching automatically */}
                <FeatureFlagProvider>
                  <App />
                </FeatureFlagProvider>
              </HeroUIProvider>
            </ThemeProvider>
          </UserProvider>
        </Router>
      </ExtensionErrorBoundary>
    </StrictMode>
  );

  if (import.meta.env.DEV) {
    console.log('🔍 DEBUG: React app render call completed');
  }
} catch (error) {
  console.error('🚨 CRITICAL ERROR in main.jsx:', error);
  console.error('🚨 Error stack:', error.stack);
}
