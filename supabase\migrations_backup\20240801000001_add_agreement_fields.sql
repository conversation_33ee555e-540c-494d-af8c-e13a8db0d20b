-- Migration to add fields required for agreement generation
-- This migration adds company information, project-specific fields, and other fields needed for agreement generation

-- Check if the projects table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'projects'
    ) THEN
        -- Add company information fields to projects table
        BEGIN
            ALTER TABLE public.projects 
            ADD COLUMN IF NOT EXISTS company_name TEXT,
            ADD COLUMN IF NOT EXISTS company_address TEXT,
            ADD COLUMN IF NOT EXISTS company_state TEXT,
            ADD COLUMN IF NOT EXISTS company_county TEXT;
            
            RAISE NOTICE 'Added company information fields to projects table';
        EXCEPTION WHEN duplicate_column THEN
            RAISE NOTICE 'Company information fields already exist in projects table';
        END;

        -- Add project-specific fields to projects table
        BEGIN
            ALTER TABLE public.projects 
            ADD COLUMN IF NOT EXISTS engine TEXT,
            ADD COLUMN IF NOT EXISTS platforms TEXT,
            ADD COLUMN IF NOT EXISTS genre TEXT,
            ADD COLUMN IF NOT EXISTS technology_stack TEXT,
            ADD COLUMN IF NOT EXISTS distribution_platforms TEXT;
            
            RAISE NOTICE 'Added project-specific fields to projects table';
        EXCEPTION WHEN duplicate_column THEN
            RAISE NOTICE 'Project-specific fields already exist in projects table';
        END;
    ELSE
        RAISE NOTICE 'Projects table does not exist';
    END IF;
END $$;

-- Check if the project_contributors table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'project_contributors'
    ) THEN
        -- Add contributor information fields to project_contributors table
        BEGIN
            ALTER TABLE public.project_contributors 
            ADD COLUMN IF NOT EXISTS title TEXT,
            ADD COLUMN IF NOT EXISTS address TEXT,
            ADD COLUMN IF NOT EXISTS state TEXT,
            ADD COLUMN IF NOT EXISTS county TEXT,
            ADD COLUMN IF NOT EXISTS is_company BOOLEAN DEFAULT FALSE,
            ADD COLUMN IF NOT EXISTS company_name TEXT,
            ADD COLUMN IF NOT EXISTS signer_name TEXT,
            ADD COLUMN IF NOT EXISTS signer_title TEXT;
            
            RAISE NOTICE 'Added contributor information fields to project_contributors table';
        EXCEPTION WHEN duplicate_column THEN
            RAISE NOTICE 'Contributor information fields already exist in project_contributors table';
        END;
    ELSE
        RAISE NOTICE 'Project_contributors table does not exist';
    END IF;
END $$;

-- Check if the royalty_models table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'royalty_models'
    ) THEN
        -- Add financial parameter fields to royalty_models table
        BEGIN
            ALTER TABLE public.royalty_models 
            ADD COLUMN IF NOT EXISTS min_payout INTEGER DEFAULT 0,
            ADD COLUMN IF NOT EXISTS max_payout INTEGER DEFAULT 0,
            ADD COLUMN IF NOT EXISTS revenue_share INTEGER DEFAULT 50;
            
            RAISE NOTICE 'Added financial parameter fields to royalty_models table';
        EXCEPTION WHEN duplicate_column THEN
            RAISE NOTICE 'Financial parameter fields already exist in royalty_models table';
        END;
    ELSE
        RAISE NOTICE 'Royalty_models table does not exist';
    END IF;
END $$;

-- Add comments to the new columns
COMMENT ON COLUMN public.projects.company_name IS 'Name of the company that owns the project';
COMMENT ON COLUMN public.projects.company_address IS 'Address of the company that owns the project';
COMMENT ON COLUMN public.projects.company_state IS 'State/province of the company that owns the project';
COMMENT ON COLUMN public.projects.company_county IS 'County/region of the company that owns the project';
COMMENT ON COLUMN public.projects.engine IS 'Game engine or software framework used for the project';
COMMENT ON COLUMN public.projects.platforms IS 'Platforms the project will be released on';
COMMENT ON COLUMN public.projects.genre IS 'Genre of the project (for music or game projects)';
COMMENT ON COLUMN public.projects.technology_stack IS 'Technology stack used for the project (for software projects)';
COMMENT ON COLUMN public.projects.distribution_platforms IS 'Distribution platforms for the project (for music projects)';

COMMENT ON COLUMN public.project_contributors.title IS 'Job title of the contributor';
COMMENT ON COLUMN public.project_contributors.address IS 'Address of the contributor';
COMMENT ON COLUMN public.project_contributors.state IS 'State/province of the contributor';
COMMENT ON COLUMN public.project_contributors.county IS 'County/region of the contributor';
COMMENT ON COLUMN public.project_contributors.is_company IS 'Whether the contributor is a company';
COMMENT ON COLUMN public.project_contributors.company_name IS 'Name of the company (if contributor is a company)';
COMMENT ON COLUMN public.project_contributors.signer_name IS 'Name of the person signing for the company';
COMMENT ON COLUMN public.project_contributors.signer_title IS 'Title of the person signing for the company';

COMMENT ON COLUMN public.royalty_models.min_payout IS 'Minimum payout threshold in cents';
COMMENT ON COLUMN public.royalty_models.max_payout IS 'Maximum payout in cents';
COMMENT ON COLUMN public.royalty_models.revenue_share IS 'Revenue share percentage for contributors';
