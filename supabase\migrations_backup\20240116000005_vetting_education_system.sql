-- Migration: Vetting & Education System
-- Description: 6-level skill verification and learning management system
-- Created: 2024-01-16
-- Integration & Services Agent

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User skill levels table - tracks current verification level for each technology
CREATE TABLE user_skill_levels (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  technology VARCHAR(100) NOT NULL,
  current_level INTEGER DEFAULT 0 CHECK (current_level >= 0 AND current_level <= 5),
  verification_date TIMESTAMP WITH TIME ZONE,
  next_assessment_date TIMESTAMP WITH TIME ZONE,
  level_progress JSONB DEFAULT '{}'::jsonb, -- Progress towards next level
  total_hours_invested INTEGER DEFAULT 0,
  mentor_id UUID REFERENCES auth.users(id), -- Assigned mentor for guidance
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, technology)
);

-- Learning paths table - defines requirements for each technology and level
CREATE TABLE learning_paths (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  technology VARCHAR(100) NOT NULL,
  level INTEGER NOT NULL CHECK (level >= 0 AND level <= 5),
  path_name VARCHAR(200) NOT NULL,
  description TEXT,
  course_requirements JSONB NOT NULL DEFAULT '[]'::jsonb,
  assessment_criteria JSONB NOT NULL DEFAULT '{}'::jsonb,
  estimated_hours INTEGER NOT NULL DEFAULT 0,
  prerequisites TEXT[],
  learning_objectives TEXT[],
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(technology, level)
);

-- Assessment results table - stores all assessment attempts and results
CREATE TABLE assessment_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  technology VARCHAR(100) NOT NULL,
  level INTEGER NOT NULL,
  assessment_type VARCHAR(50) NOT NULL, -- 'automated', 'peer_review', 'expert_interview', 'project_review'
  score DECIMAL(5,2),
  max_score DECIMAL(5,2) DEFAULT 100.00,
  passed BOOLEAN NOT NULL DEFAULT false,
  assessment_data JSONB DEFAULT '{}'::jsonb,
  feedback TEXT,
  reviewer_id UUID REFERENCES auth.users(id), -- Who conducted the assessment
  time_taken_minutes INTEGER,
  attempt_number INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning progress table - tracks progress through courses and modules
CREATE TABLE learning_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  learning_path_id UUID NOT NULL REFERENCES learning_paths(id) ON DELETE CASCADE,
  course_id VARCHAR(200), -- External course ID (LinkedIn Learning, etc.)
  course_name VARCHAR(300),
  progress_percentage DECIMAL(5,2) DEFAULT 0.00,
  hours_completed DECIMAL(6,2) DEFAULT 0.00,
  status VARCHAR(50) DEFAULT 'not_started', -- 'not_started', 'in_progress', 'completed', 'paused'
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  certificate_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Peer reviews table - manages peer review process for level advancement
CREATE TABLE peer_reviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  reviewee_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  reviewer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  technology VARCHAR(100) NOT NULL,
  target_level INTEGER NOT NULL,
  review_type VARCHAR(50) NOT NULL, -- 'code_review', 'project_review', 'mentoring_assessment'
  
  -- Review criteria scores (1-5 scale)
  technical_skill_score INTEGER CHECK (technical_skill_score >= 1 AND technical_skill_score <= 5),
  communication_score INTEGER CHECK (communication_score >= 1 AND communication_score <= 5),
  collaboration_score INTEGER CHECK (collaboration_score >= 1 AND collaboration_score <= 5),
  problem_solving_score INTEGER CHECK (problem_solving_score >= 1 AND problem_solving_score <= 5),
  
  overall_score DECIMAL(3,2), -- Calculated average
  recommendation VARCHAR(50), -- 'approve', 'reject', 'needs_improvement'
  detailed_feedback TEXT,
  review_data JSONB DEFAULT '{}'::jsonb, -- Additional review-specific data
  
  status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'completed', 'disputed'
  submitted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(reviewee_id, reviewer_id, technology, target_level, review_type)
);

-- Expert interviews table - manages expert-level assessments
CREATE TABLE expert_interviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  candidate_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  expert_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  technology VARCHAR(100) NOT NULL,
  target_level INTEGER NOT NULL CHECK (target_level >= 4), -- Only for levels 4-5
  
  scheduled_at TIMESTAMP WITH TIME ZONE,
  duration_minutes INTEGER DEFAULT 90,
  interview_type VARCHAR(50) DEFAULT 'technical', -- 'technical', 'architectural', 'leadership'
  
  -- Assessment areas
  technical_depth_score INTEGER CHECK (technical_depth_score >= 1 AND technical_depth_score <= 10),
  system_design_score INTEGER CHECK (system_design_score >= 1 AND system_design_score <= 10),
  leadership_score INTEGER CHECK (leadership_score >= 1 AND leadership_score <= 10),
  communication_score INTEGER CHECK (communication_score >= 1 AND communication_score <= 10),
  
  overall_score DECIMAL(4,2), -- Calculated average
  recommendation VARCHAR(50), -- 'approve', 'reject', 'conditional_approve'
  detailed_feedback TEXT,
  interview_notes TEXT,
  recording_url TEXT,
  
  status VARCHAR(50) DEFAULT 'scheduled', -- 'scheduled', 'completed', 'cancelled', 'rescheduled'
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Portfolio projects table - tracks projects submitted for verification
CREATE TABLE portfolio_projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  technology VARCHAR(100) NOT NULL,
  level INTEGER NOT NULL,
  
  project_name VARCHAR(300) NOT NULL,
  description TEXT,
  project_url TEXT,
  repository_url TEXT,
  demo_url TEXT,
  
  -- Project metadata
  technologies_used TEXT[],
  project_type VARCHAR(100), -- 'web_app', 'mobile_app', 'api', 'library', 'game', etc.
  complexity_score INTEGER CHECK (complexity_score >= 1 AND complexity_score <= 10),
  estimated_hours INTEGER,
  
  -- Review status
  review_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'needs_revision'
  reviewer_id UUID REFERENCES auth.users(id),
  review_feedback TEXT,
  review_score DECIMAL(3,2),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Mentoring relationships table - tracks mentor-mentee relationships
CREATE TABLE mentoring_relationships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mentor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  mentee_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  technology VARCHAR(100) NOT NULL,
  
  relationship_type VARCHAR(50) DEFAULT 'formal', -- 'formal', 'informal', 'peer'
  status VARCHAR(50) DEFAULT 'active', -- 'active', 'completed', 'paused', 'terminated'
  
  -- Mentoring goals and progress
  goals TEXT[],
  progress_notes TEXT,
  total_hours DECIMAL(6,2) DEFAULT 0.00,
  sessions_completed INTEGER DEFAULT 0,
  
  -- Feedback and ratings
  mentor_rating DECIMAL(3,2), -- Mentee's rating of mentor
  mentee_rating DECIMAL(3,2), -- Mentor's rating of mentee
  mentor_feedback TEXT,
  mentee_feedback TEXT,
  
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ended_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(mentor_id, mentee_id, technology)
);

-- Industry certifications table - tracks external certifications
CREATE TABLE industry_certifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  certification_name VARCHAR(300) NOT NULL,
  issuing_organization VARCHAR(200) NOT NULL,
  technology VARCHAR(100),
  
  certification_id VARCHAR(200), -- External certification ID
  issue_date DATE,
  expiry_date DATE,
  verification_url TEXT,
  certificate_url TEXT,
  
  -- Verification status
  verification_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'verified', 'expired', 'invalid'
  verified_at TIMESTAMP WITH TIME ZONE,
  verified_by UUID REFERENCES auth.users(id),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Level advancement requests table - manages level advancement workflow
CREATE TABLE level_advancement_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  technology VARCHAR(100) NOT NULL,
  current_level INTEGER NOT NULL,
  target_level INTEGER NOT NULL,
  
  -- Requirements completion tracking
  requirements_met JSONB DEFAULT '{}'::jsonb,
  evidence_submitted JSONB DEFAULT '{}'::jsonb,
  
  status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'under_review', 'approved', 'rejected', 'needs_more_evidence'
  reviewer_id UUID REFERENCES auth.users(id),
  review_notes TEXT,
  
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  approved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_user_skill_levels_user_id ON user_skill_levels(user_id);
CREATE INDEX idx_user_skill_levels_technology ON user_skill_levels(technology);
CREATE INDEX idx_user_skill_levels_level ON user_skill_levels(current_level);

CREATE INDEX idx_learning_paths_technology ON learning_paths(technology);
CREATE INDEX idx_learning_paths_level ON learning_paths(level);
CREATE INDEX idx_learning_paths_active ON learning_paths(is_active);

CREATE INDEX idx_assessment_results_user_id ON assessment_results(user_id);
CREATE INDEX idx_assessment_results_technology ON assessment_results(technology);
CREATE INDEX idx_assessment_results_level ON assessment_results(level);
CREATE INDEX idx_assessment_results_passed ON assessment_results(passed);

CREATE INDEX idx_learning_progress_user_id ON learning_progress(user_id);
CREATE INDEX idx_learning_progress_path_id ON learning_progress(learning_path_id);
CREATE INDEX idx_learning_progress_status ON learning_progress(status);

CREATE INDEX idx_peer_reviews_reviewee_id ON peer_reviews(reviewee_id);
CREATE INDEX idx_peer_reviews_reviewer_id ON peer_reviews(reviewer_id);
CREATE INDEX idx_peer_reviews_technology ON peer_reviews(technology);
CREATE INDEX idx_peer_reviews_status ON peer_reviews(status);

CREATE INDEX idx_expert_interviews_candidate_id ON expert_interviews(candidate_id);
CREATE INDEX idx_expert_interviews_expert_id ON expert_interviews(expert_id);
CREATE INDEX idx_expert_interviews_scheduled_at ON expert_interviews(scheduled_at);
CREATE INDEX idx_expert_interviews_status ON expert_interviews(status);

CREATE INDEX idx_portfolio_projects_user_id ON portfolio_projects(user_id);
CREATE INDEX idx_portfolio_projects_technology ON portfolio_projects(technology);
CREATE INDEX idx_portfolio_projects_review_status ON portfolio_projects(review_status);

CREATE INDEX idx_mentoring_relationships_mentor_id ON mentoring_relationships(mentor_id);
CREATE INDEX idx_mentoring_relationships_mentee_id ON mentoring_relationships(mentee_id);
CREATE INDEX idx_mentoring_relationships_status ON mentoring_relationships(status);

CREATE INDEX idx_industry_certifications_user_id ON industry_certifications(user_id);
CREATE INDEX idx_industry_certifications_verification_status ON industry_certifications(verification_status);

CREATE INDEX idx_level_advancement_requests_user_id ON level_advancement_requests(user_id);
CREATE INDEX idx_level_advancement_requests_status ON level_advancement_requests(status);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_skill_levels_updated_at BEFORE UPDATE ON user_skill_levels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_paths_updated_at BEFORE UPDATE ON learning_paths FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_learning_progress_updated_at BEFORE UPDATE ON learning_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_portfolio_projects_updated_at BEFORE UPDATE ON portfolio_projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_mentoring_relationships_updated_at BEFORE UPDATE ON mentoring_relationships FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_industry_certifications_updated_at BEFORE UPDATE ON industry_certifications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE user_skill_levels ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE peer_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE expert_interviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolio_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE mentoring_relationships ENABLE ROW LEVEL SECURITY;
ALTER TABLE industry_certifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE level_advancement_requests ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can view their own skill levels" ON user_skill_levels FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their own skill levels" ON user_skill_levels FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Learning paths are viewable by all authenticated users" ON learning_paths FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can view their own assessment results" ON assessment_results FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own assessment results" ON assessment_results FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own learning progress" ON learning_progress FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view peer reviews they're involved in" ON peer_reviews FOR SELECT USING (auth.uid() = reviewee_id OR auth.uid() = reviewer_id);
CREATE POLICY "Users can create peer reviews" ON peer_reviews FOR INSERT WITH CHECK (auth.uid() = reviewer_id);
CREATE POLICY "Users can update their own peer reviews" ON peer_reviews FOR UPDATE USING (auth.uid() = reviewer_id);

CREATE POLICY "Users can view expert interviews they're involved in" ON expert_interviews FOR SELECT USING (auth.uid() = candidate_id OR auth.uid() = expert_id);

CREATE POLICY "Users can manage their own portfolio projects" ON portfolio_projects FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view mentoring relationships they're involved in" ON mentoring_relationships FOR SELECT USING (auth.uid() = mentor_id OR auth.uid() = mentee_id);
CREATE POLICY "Users can update mentoring relationships they're involved in" ON mentoring_relationships FOR UPDATE USING (auth.uid() = mentor_id OR auth.uid() = mentee_id);

CREATE POLICY "Users can manage their own certifications" ON industry_certifications FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own advancement requests" ON level_advancement_requests FOR ALL USING (auth.uid() = user_id);

-- Table comments
COMMENT ON TABLE user_skill_levels IS 'Tracks current verification level for each user and technology';
COMMENT ON TABLE learning_paths IS 'Defines learning requirements for each technology and level';
COMMENT ON TABLE assessment_results IS 'Stores all assessment attempts and results';
COMMENT ON TABLE learning_progress IS 'Tracks progress through courses and learning modules';
COMMENT ON TABLE peer_reviews IS 'Manages peer review process for level advancement';
COMMENT ON TABLE expert_interviews IS 'Manages expert-level technical assessments';
COMMENT ON TABLE portfolio_projects IS 'Tracks projects submitted for skill verification';
COMMENT ON TABLE mentoring_relationships IS 'Manages mentor-mentee relationships';
COMMENT ON TABLE industry_certifications IS 'Tracks external industry certifications';
COMMENT ON TABLE level_advancement_requests IS 'Manages level advancement workflow';
