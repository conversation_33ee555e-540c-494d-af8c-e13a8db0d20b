import { test, expect } from '@playwright/test';

/**
 * Accessibility Compliance Test Suite
 * Verifies WCAG contrast ratio requirements for the project wizard
 */

test.describe('Project Wizard Accessibility Compliance', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to project wizard
    await page.goto('/#/project/wizard');
    await page.waitForLoadState('networkidle');
    
    // Login if needed
    const loginButton = page.locator('text=Login');
    if (await loginButton.isVisible()) {
      await loginButton.click();
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'TestPassword123!');
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
      await page.goto('/#/project/wizard');
      await page.waitForLoadState('networkidle');
    }
  });

  test('Light Mode - Studio Selection Cards Meet WCAG Contrast Requirements', async ({ page }) => {
    // Ensure we're in light mode
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
      document.documentElement.classList.add('light');
    });
    
    await page.waitForTimeout(500); // Allow theme to apply
    
    // Test Individual/Freelancer card
    const individualCard = page.locator('text=Individual/Freelancer').first();
    await expect(individualCard).toBeVisible();
    
    const individualTitle = await individualCard.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        color: styles.color,
        backgroundColor: styles.backgroundColor,
        fontSize: styles.fontSize,
        fontWeight: styles.fontWeight
      };
    });
    
    console.log('Individual Card Title (Light Mode):', individualTitle);
    
    // Test description text
    const individualDesc = page.locator('text=Working as yourself, no formal business entity').first();
    const individualDescStyles = await individualDesc.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        color: styles.color,
        backgroundColor: styles.backgroundColor,
        fontSize: styles.fontSize
      };
    });
    
    console.log('Individual Card Description (Light Mode):', individualDescStyles);
    
    // Test Established Business card
    const businessCard = page.locator('text=Established Business').first();
    await expect(businessCard).toBeVisible();
    
    const businessTitle = await businessCard.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        color: styles.color,
        backgroundColor: styles.backgroundColor,
        fontSize: styles.fontSize,
        fontWeight: styles.fontWeight
      };
    });
    
    console.log('Business Card Title (Light Mode):', businessTitle);
    
    // Test business description
    const businessDesc = page.locator('text=LLC, Corporation, Partnership, or other business entity').first();
    const businessDescStyles = await businessDesc.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        color: styles.color,
        backgroundColor: styles.backgroundColor,
        fontSize: styles.fontSize
      };
    });
    
    console.log('Business Card Description (Light Mode):', businessDescStyles);
  });

  test('Dark Mode - Studio Selection Cards Meet WCAG Contrast Requirements', async ({ page }) => {
    // Switch to dark mode
    await page.evaluate(() => {
      document.documentElement.classList.remove('light');
      document.documentElement.classList.add('dark');
    });
    
    await page.waitForTimeout(500); // Allow theme to apply
    
    // Test Individual/Freelancer card in dark mode
    const individualCard = page.locator('text=Individual/Freelancer').first();
    await expect(individualCard).toBeVisible();
    
    const individualTitle = await individualCard.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        color: styles.color,
        backgroundColor: styles.backgroundColor,
        fontSize: styles.fontSize,
        fontWeight: styles.fontWeight
      };
    });
    
    console.log('Individual Card Title (Dark Mode):', individualTitle);
    
    // Test description text in dark mode
    const individualDesc = page.locator('text=Working as yourself, no formal business entity').first();
    const individualDescStyles = await individualDesc.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        color: styles.color,
        backgroundColor: styles.backgroundColor,
        fontSize: styles.fontSize
      };
    });
    
    console.log('Individual Card Description (Dark Mode):', individualDescStyles);
    
    // Test Established Business card in dark mode
    const businessCard = page.locator('text=Established Business').first();
    await expect(businessCard).toBeVisible();
    
    const businessTitle = await businessCard.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        color: styles.color,
        backgroundColor: styles.backgroundColor,
        fontSize: styles.fontSize,
        fontWeight: styles.fontWeight
      };
    });
    
    console.log('Business Card Title (Dark Mode):', businessTitle);
    
    // Test business description in dark mode
    const businessDesc = page.locator('text=LLC, Corporation, Partnership, or other business entity').first();
    const businessDescStyles = await businessDesc.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        color: styles.color,
        backgroundColor: styles.backgroundColor,
        fontSize: styles.fontSize
      };
    });
    
    console.log('Business Card Description (Dark Mode):', businessDescStyles);
  });

  test('Selection States - Visual Feedback and Contrast', async ({ page }) => {
    // Test selection of Individual/Freelancer
    const individualRadio = page.locator('input[value="individual"]');
    await individualRadio.click();
    await page.waitForTimeout(300); // Allow selection animation
    
    // Verify selection visual feedback
    const selectedCard = page.locator('text=Individual/Freelancer').first().locator('..');
    const selectedStyles = await selectedCard.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        borderColor: styles.borderColor,
        backgroundColor: styles.backgroundColor,
        boxShadow: styles.boxShadow
      };
    });
    
    console.log('Selected Card Styles:', selectedStyles);
    
    // Test selection of Established Business
    const businessRadio = page.locator('input[value="established_business"]');
    await businessRadio.click();
    await page.waitForTimeout(300); // Allow selection animation
    
    // Verify business selection visual feedback
    const selectedBusinessCard = page.locator('text=Established Business').first().locator('..');
    const selectedBusinessStyles = await selectedBusinessCard.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        borderColor: styles.borderColor,
        backgroundColor: styles.backgroundColor,
        boxShadow: styles.boxShadow
      };
    });
    
    console.log('Selected Business Card Styles:', selectedBusinessStyles);
  });

  test('Header Text Accessibility', async ({ page }) => {
    // Test main header
    const mainHeader = page.locator('text=Choose Your Studio').first();
    await expect(mainHeader).toBeVisible();
    
    const headerStyles = await mainHeader.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        color: styles.color,
        fontSize: styles.fontSize,
        fontWeight: styles.fontWeight
      };
    });
    
    console.log('Main Header Styles:', headerStyles);
    
    // Test section description
    const description = page.locator('text=Studios help us generate legal agreements').first();
    const descStyles = await description.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        color: styles.color,
        fontSize: styles.fontSize
      };
    });
    
    console.log('Description Styles:', descStyles);
  });

  test('Interactive Elements - Focus and Hover States', async ({ page }) => {
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Check focus styles on radio buttons
    const focusedElement = page.locator(':focus');
    const focusStyles = await focusedElement.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        outline: styles.outline,
        outlineColor: styles.outlineColor,
        boxShadow: styles.boxShadow
      };
    });
    
    console.log('Focus Styles:', focusStyles);
    
    // Test hover states
    const individualCard = page.locator('text=Individual/Freelancer').first().locator('..');
    await individualCard.hover();
    
    const hoverStyles = await individualCard.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return {
        borderColor: styles.borderColor,
        backgroundColor: styles.backgroundColor
      };
    });
    
    console.log('Hover Styles:', hoverStyles);
  });
});
